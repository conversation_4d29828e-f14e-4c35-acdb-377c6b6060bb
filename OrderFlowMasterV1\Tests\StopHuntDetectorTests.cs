using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Comprehensive Unit Tests for StopHuntDetector
    /// 
    /// Validates all critical functionality according to ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md:
    /// - Performance: <0.4ms execution time
    /// - Accuracy: >85% stop concentration detection accuracy
    /// - Round Number Analysis: $50 increment clustering for ETH
    /// - Data Compliance: 100% real live data validation
    /// - Threshold: >15 orders per price level detection
    /// </summary>
    [TestClass]
    public class StopHuntDetectorTests
    {
        private StopHuntDetector _detector;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;
        private MockDataSeries _mockVolume;
        private MockDataSeries _mockHigh;
        private MockDataSeries _mockLow;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = false
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                CurrentVolumeThreshold = 1000m,
                CurrentDeltaThreshold = 500m
            };

            _mockStrategy = new MockChartStrategy();
            _mockVolume = new MockDataSeries();
            _mockHigh = new MockDataSeries();
            _mockLow = new MockDataSeries();

            _detector = new StopHuntDetector(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _detector?.Dispose();
        }

        #region Performance Validation Tests

        /// <summary>
        /// Test 1.1: Validate execution time <0.4ms per calculation
        /// CRITICAL: Must meet performance target for stop hunt detection
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidatePerformanceTarget()
        {
            // Arrange
            GenerateRealisticMarketData(100);
            var currentPrice = 2500m;
            var testBar = 60;

            // Act
            var startTime = DateTime.UtcNow;
            var result = _detector.DetectStopHuntSetup(testBar, currentPrice, _mockVolume, _mockHigh, _mockLow);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(executionTime < 0.4, 
                $"Execution time {executionTime:F3}ms exceeds 0.4ms target");
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(testBar, result.Bar, "Bar index should match");
        }

        /// <summary>
        /// Test 1.2: Validate stop concentration detection accuracy >85%
        /// Uses known stop hunt scenarios from realistic market data
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateStopConcentrationAccuracy()
        {
            // Arrange
            var stopHuntScenarios = GenerateKnownStopHuntScenarios();
            int correctDetections = 0;
            int totalScenarios = stopHuntScenarios.Count;

            // Act
            foreach (var scenario in stopHuntScenarios)
            {
                SetupScenarioData(scenario);
                var result = _detector.DetectStopHuntSetup(scenario.TestBar, scenario.Price, _mockVolume, _mockHigh, _mockLow);
                
                if (result.HasStopHuntSetup == scenario.ExpectedStopHunt)
                {
                    correctDetections++;
                }
            }

            // Assert
            var accuracy = (decimal)correctDetections / totalScenarios;
            Assert.IsTrue(accuracy > 0.85m, 
                $"Stop hunt detection accuracy {accuracy:P} must be >85% (got {correctDetections}/{totalScenarios})");
        }

        #endregion

        #region Round Number Analysis Tests

        /// <summary>
        /// Test 1.3: Validate $50 increment round number clustering for ETH
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateRoundNumberClustering()
        {
            // Arrange - Test prices near $50 increments
            var testPrices = new decimal[] { 2500m, 2550m, 2600m, 2499.5m, 2550.2m };
            GenerateRealisticMarketData(100);

            foreach (var price in testPrices)
            {
                // Act
                var result = _detector.DetectStopHuntSetup(60, price, _mockVolume, _mockHigh, _mockLow);

                // Assert
                Assert.IsTrue(result.RoundNumberCluster > 0.5m, 
                    $"Round number clustering should be significant for price ${price:F2} (got {result.RoundNumberCluster:F2})");
            }
        }

        /// <summary>
        /// Test 1.4: Validate round number clustering strength calculation
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateClusteringStrength()
        {
            // Arrange
            GenerateRealisticMarketData(100);
            
            // Test exact round number (should have maximum clustering)
            var exactRoundNumber = 2500m;
            var result1 = _detector.DetectStopHuntSetup(60, exactRoundNumber, _mockVolume, _mockHigh, _mockLow);

            // Test price far from round number (should have minimal clustering)
            var farFromRoundNumber = 2525m; // Exactly between 2500 and 2550
            var result2 = _detector.DetectStopHuntSetup(61, farFromRoundNumber, _mockVolume, _mockHigh, _mockLow);

            // Assert
            Assert.IsTrue(result1.RoundNumberCluster > result2.RoundNumberCluster,
                $"Exact round number clustering ({result1.RoundNumberCluster:F2}) should be higher than mid-point ({result2.RoundNumberCluster:F2})");
        }

        #endregion

        #region Stop Hunt Direction Tests

        /// <summary>
        /// Test 1.5: Validate stop hunt direction detection
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateDirectionDetection()
        {
            // Arrange
            GenerateRealisticMarketData(100);

            // Test price just above round number (likely downward hunt)
            var priceAboveRound = 2510m; // Just above 2500
            var result1 = _detector.DetectStopHuntSetup(60, priceAboveRound, _mockVolume, _mockHigh, _mockLow);

            // Test price just below round number (likely upward hunt)
            var priceBelowRound = 2540m; // Just below 2550
            var result2 = _detector.DetectStopHuntSetup(61, priceBelowRound, _mockVolume, _mockHigh, _mockLow);

            // Assert
            Assert.AreEqual(StopHuntDirection.Downward, result1.HuntDirection,
                "Price above round number should suggest downward hunt");
            Assert.AreEqual(StopHuntDirection.Upward, result2.HuntDirection,
                "Price below round number should suggest upward hunt");
        }

        #endregion

        #region Probability Threshold Tests

        /// <summary>
        /// Test 1.6: Validate 75% probability threshold detection
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateProbabilityThreshold()
        {
            // Arrange - Create high-probability stop hunt scenario
            GenerateHighProbabilityStopHuntScenario();
            var currentPrice = 2500m; // Exact round number
            var testBar = 60;

            // Act
            var result = _detector.DetectStopHuntSetup(testBar, currentPrice, _mockVolume, _mockHigh, _mockLow);

            // Assert
            Assert.IsTrue(result.StopHuntProbability >= 0.75m, 
                $"Stop hunt probability {result.StopHuntProbability:P} should be >=75% threshold");
            Assert.IsTrue(result.HasStopHuntSetup, "Should detect stop hunt setup at 75% threshold");
            Assert.IsTrue(result.SignalStrength > 0.7m, "Signal strength should be significant");
        }

        #endregion

        #region Real Data Compliance Tests

        /// <summary>
        /// Test 1.7: Validate 100% real live data compliance
        /// CRITICAL: Must reject synthetic/mock data and trigger emergency shutdown
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void StopHuntDetector_RejectSyntheticData()
        {
            // Arrange - Generate obvious synthetic data pattern
            GenerateSyntheticDataPattern();
            var currentPrice = 2500m;

            // Act - Should trigger emergency shutdown
            _detector.DetectStopHuntSetup(60, currentPrice, _mockVolume, _mockHigh, _mockLow);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        /// <summary>
        /// Test 1.8: Validate unrealistic price rejection
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void StopHuntDetector_RejectUnrealisticPrice()
        {
            // Arrange
            GenerateRealisticMarketData(100);
            var unrealisticPrice = 50000m; // Unrealistic ETH price

            // Act - Should trigger emergency shutdown
            _detector.DetectStopHuntSetup(60, unrealisticPrice, _mockVolume, _mockHigh, _mockLow);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        #endregion

        #region Integration Tests

        /// <summary>
        /// Test 1.9: Validate integration methods
        /// </summary>
        [TestMethod]
        public void StopHuntDetector_ValidateIntegrationMethods()
        {
            // Arrange
            GenerateHighProbabilityStopHuntScenario();
            var currentPrice = 2500m;
            var testBar = 60;

            // Act
            var result = _detector.DetectStopHuntSetup(testBar, currentPrice, _mockVolume, _mockHigh, _mockLow);
            var probability = _detector.GetStopHuntProbability(testBar);
            var hasSetup = _detector.HasStopHuntSetup(testBar);

            // Assert
            Assert.AreEqual(result.StopHuntProbability, probability, "GetStopHuntProbability should match result");
            Assert.AreEqual(result.HasStopHuntSetup, hasSetup, "HasStopHuntSetup should match result");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generate realistic market data for testing
        /// </summary>
        private void GenerateRealisticMarketData(int barCount)
        {
            var random = new Random(42); // Fixed seed for reproducible tests
            var basePrice = 2500m;
            
            for (int i = 0; i < barCount; i++)
            {
                // Generate realistic OHLC data
                var open = basePrice + (decimal)((random.NextDouble() - 0.5) * 20); // ±$10 variation
                var high = open + (decimal)(random.NextDouble() * 5); // Up to $5 higher
                var low = open - (decimal)(random.NextDouble() * 5);  // Up to $5 lower
                var volume = 500m + (decimal)(random.NextDouble() * 1500); // 500-2000 volume

                _mockHigh.SetValue(i, high);
                _mockLow.SetValue(i, low);
                _mockVolume.SetValue(i, volume);

                basePrice = open; // Drift price for next bar
            }
        }

        /// <summary>
        /// Generate high-probability stop hunt scenario
        /// </summary>
        private void GenerateHighProbabilityStopHuntScenario()
        {
            GenerateRealisticMarketData(100);
            
            // Create volume concentration around current price level
            for (int i = 55; i < 65; i++)
            {
                _mockVolume.SetValue(i, 3000m); // High volume concentration
                _mockHigh.SetValue(i, 2505m);   // Near round number
                _mockLow.SetValue(i, 2495m);    // Near round number
            }
        }

        /// <summary>
        /// Generate known stop hunt scenarios for accuracy testing
        /// </summary>
        private List<StopHuntScenario> GenerateKnownStopHuntScenarios()
        {
            return new List<StopHuntScenario>
            {
                new StopHuntScenario { TestBar = 60, Price = 2500m, ExpectedStopHunt = true, Description = "Exact round number with volume concentration" },
                new StopHuntScenario { TestBar = 70, Price = 2550m, ExpectedStopHunt = true, Description = "Round number with high clustering" },
                new StopHuntScenario { TestBar = 80, Price = 2525m, ExpectedStopHunt = false, Description = "Mid-point between round numbers" },
                new StopHuntScenario { TestBar = 90, Price = 2487m, ExpectedStopHunt = false, Description = "Random price with low concentration" }
            };
        }

        /// <summary>
        /// Setup data for specific stop hunt scenario
        /// </summary>
        private void SetupScenarioData(StopHuntScenario scenario)
        {
            GenerateRealisticMarketData(100);
            
            if (scenario.ExpectedStopHunt)
            {
                // Create conditions favorable for stop hunt detection
                for (int i = scenario.TestBar - 10; i < scenario.TestBar + 5; i++)
                {
                    if (i >= 0)
                    {
                        _mockVolume.SetValue(i, 2500m); // High volume
                        _mockHigh.SetValue(i, scenario.Price + 2m);
                        _mockLow.SetValue(i, scenario.Price - 2m);
                    }
                }
            }
        }

        /// <summary>
        /// Generate obvious synthetic data pattern for compliance testing
        /// </summary>
        private void GenerateSyntheticDataPattern()
        {
            // Create identical values (common in mock data)
            for (int i = 0; i < 100; i++)
            {
                _mockVolume.SetValue(i, 1000m); // Identical volume
                _mockHigh.SetValue(i, 2505m);   // Identical high
                _mockLow.SetValue(i, 2495m);    // Identical low
            }
        }

        #endregion

        #region Test Data Structures

        private class StopHuntScenario
        {
            public int TestBar { get; set; }
            public decimal Price { get; set; }
            public bool ExpectedStopHunt { get; set; }
            public string Description { get; set; }
        }

        #endregion
    }
}
