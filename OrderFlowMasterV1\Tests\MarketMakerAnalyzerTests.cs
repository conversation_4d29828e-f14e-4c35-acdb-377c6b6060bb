using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Comprehensive Unit Tests for MarketMakerAnalyzer
    /// 
    /// Validates all critical functionality according to ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md:
    /// - Performance: <0.3ms execution time
    /// - Accuracy: >80% MM behavior tracking accuracy
    /// - Quote Lifespan: 1-hour baseline analysis functional
    /// - Withdrawal Detection: >4x reduction threshold detection
    /// - Data Compliance: 100% real live data validation
    /// </summary>
    [TestClass]
    public class MarketMakerAnalyzerTests
    {
        private MarketMakerAnalyzer _analyzer;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = false
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                CurrentVolumeThreshold = 1000m,
                CurrentDeltaThreshold = 500m
            };

            _mockStrategy = new MockChartStrategy();
            _analyzer = new MarketMakerAnalyzer(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _analyzer?.Dispose();
        }

        #region Performance Validation Tests

        /// <summary>
        /// Test 1.1: Validate execution time <0.3ms per calculation
        /// CRITICAL: Must meet performance target for MM behavior analysis
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidatePerformanceTarget()
        {
            // Arrange
            var bidPrice = 2499.50m;
            var askPrice = 2500.50m;
            var bidVolume = 1000m;
            var askVolume = 1200m;
            var timestamp = DateTime.UtcNow;
            var testBar = 250; // Sufficient for baseline

            // Act
            var startTime = DateTime.UtcNow;
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, bidPrice, askPrice, bidVolume, askVolume, timestamp);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(executionTime < 0.3, 
                $"Execution time {executionTime:F3}ms exceeds 0.3ms target");
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(testBar, result.Bar, "Bar index should match");
        }

        /// <summary>
        /// Test 1.2: Validate MM behavior tracking accuracy >80%
        /// Uses known MM withdrawal scenarios
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateMMBehaviorAccuracy()
        {
            // Arrange
            var mmScenarios = GenerateKnownMMScenarios();
            int correctDetections = 0;
            int totalScenarios = mmScenarios.Count;

            // Act
            foreach (var scenario in mmScenarios)
            {
                var result = _analyzer.CalculateMMWithdrawalSignal(
                    scenario.Bar, scenario.BidPrice, scenario.AskPrice,
                    scenario.BidVolume, scenario.AskVolume, scenario.Timestamp);
                
                if (result.HasWithdrawalSignal == scenario.ExpectedWithdrawal)
                {
                    correctDetections++;
                }
            }

            // Assert
            var accuracy = (decimal)correctDetections / totalScenarios;
            Assert.IsTrue(accuracy > 0.80m, 
                $"MM behavior tracking accuracy {accuracy:P} must be >80% (got {correctDetections}/{totalScenarios})");
        }

        #endregion

        #region Withdrawal Signal Tests

        /// <summary>
        /// Test 1.3: Validate 4x withdrawal threshold detection
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateWithdrawalThreshold()
        {
            // Arrange - Create scenario with exactly 4x lifespan reduction
            var baselineBar = 250;
            var testBar = 260;
            
            // Establish baseline (1 second quote lifespan)
            for (int i = 240; i < 250; i++)
            {
                var timestamp = DateTime.UtcNow.AddSeconds(-10 + (i - 240));
                _analyzer.CalculateMMWithdrawalSignal(i, 2499.50m, 2500.50m, 1000m, 1000m, timestamp);
            }

            // Create 4x reduction scenario (0.25 second lifespan)
            var withdrawalTimestamp = DateTime.UtcNow.AddMilliseconds(-250); // 0.25 second ago
            
            // Act
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, 2499.50m, 2500.50m, 1000m, 1000m, withdrawalTimestamp);

            // Assert
            Assert.IsTrue(result.LifespanReduction >= 4.0m, 
                $"Lifespan reduction {result.LifespanReduction:F2}x should be >=4x threshold");
            Assert.IsTrue(result.HasWithdrawalSignal, "Should detect withdrawal signal at 4x threshold");
            Assert.IsTrue(result.WithdrawalStrength > 0.3m, "Withdrawal strength should be significant");
        }

        /// <summary>
        /// Test 1.4: Validate significant withdrawal detection (6x threshold)
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateSignificantWithdrawal()
        {
            // Arrange - Create scenario with 8x lifespan reduction
            var baselineBar = 250;
            var testBar = 260;
            
            // Establish baseline
            for (int i = 240; i < 250; i++)
            {
                var timestamp = DateTime.UtcNow.AddSeconds(-10 + (i - 240));
                _analyzer.CalculateMMWithdrawalSignal(i, 2499.50m, 2500.50m, 1000m, 1000m, timestamp);
            }

            // Create 8x reduction scenario (0.125 second lifespan)
            var withdrawalTimestamp = DateTime.UtcNow.AddMilliseconds(-125); // 0.125 second ago
            
            // Act
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, 2499.50m, 2500.50m, 1000m, 1000m, withdrawalTimestamp);

            // Assert
            Assert.IsTrue(result.IsSignificantWithdrawal, "Should detect significant withdrawal at 8x reduction");
            Assert.IsTrue(result.HasWithdrawalSignal, "Should detect withdrawal signal");
            Assert.IsTrue(result.WithdrawalStrength > 0.6m, "Withdrawal strength should be very high");
        }

        #endregion

        #region Spread Analysis Tests

        /// <summary>
        /// Test 1.5: Validate bid-ask spread analysis
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateSpreadAnalysis()
        {
            // Arrange - Normal spread scenario
            var normalBid = 2499.50m;
            var normalAsk = 2500.50m; // $1.00 spread
            var testBar = 250;

            // Act
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, normalBid, normalAsk, 1000m, 1000m, DateTime.UtcNow);

            // Assert
            Assert.AreEqual(1.00m, result.CurrentSpread, "Current spread should be calculated correctly");
            Assert.IsTrue(result.SpreadExpansion > 0, "Spread expansion should be positive");
        }

        /// <summary>
        /// Test 1.6: Validate liquidity quality analysis
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateLiquidityQuality()
        {
            // Arrange - Balanced liquidity scenario
            var balancedBidVolume = 1000m;
            var balancedAskVolume = 1000m;
            var testBar = 250;

            // Act
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, 2499.50m, 2500.50m, 
                balancedBidVolume, balancedAskVolume, DateTime.UtcNow);

            // Assert
            Assert.IsTrue(result.LiquidityQuality > 0.5m, "Balanced liquidity should have good quality score");
            Assert.IsTrue(result.MMHealthScore > 0.3m, "MM health score should be reasonable");
        }

        #endregion

        #region Real Data Compliance Tests

        /// <summary>
        /// Test 1.7: Validate 100% real live data compliance
        /// CRITICAL: Must reject synthetic/mock data and trigger emergency shutdown
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void MarketMakerAnalyzer_RejectSyntheticData()
        {
            // Arrange - Generate obvious synthetic data pattern (perfect round numbers)
            var syntheticBidPrice = 2500.00m;
            var syntheticAskPrice = 2501.00m;
            var syntheticBidVolume = 1000m; // Perfect round number
            var syntheticAskVolume = 1000m; // Perfect round number

            // Act - Should trigger emergency shutdown
            _analyzer.CalculateMMWithdrawalSignal(250, syntheticBidPrice, syntheticAskPrice, 
                syntheticBidVolume, syntheticAskVolume, DateTime.UtcNow);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        /// <summary>
        /// Test 1.8: Validate unrealistic price rejection
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void MarketMakerAnalyzer_RejectUnrealisticPrices()
        {
            // Arrange - Unrealistic ETH price
            var unrealisticBid = 50000m; // Unrealistic ETH price
            var unrealisticAsk = 50001m;

            // Act - Should trigger emergency shutdown
            _analyzer.CalculateMMWithdrawalSignal(250, unrealisticBid, unrealisticAsk, 1000m, 1000m, DateTime.UtcNow);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        /// <summary>
        /// Test 1.9: Validate invalid spread rejection
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void MarketMakerAnalyzer_RejectInvalidSpread()
        {
            // Arrange - Invalid spread (ask <= bid)
            var invalidBid = 2500.50m;
            var invalidAsk = 2500.00m; // Ask lower than bid

            // Act - Should trigger emergency shutdown
            _analyzer.CalculateMMWithdrawalSignal(250, invalidBid, invalidAsk, 1000m, 1000m, DateTime.UtcNow);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        #endregion

        #region Integration Tests

        /// <summary>
        /// Test 1.10: Validate integration methods
        /// </summary>
        [TestMethod]
        public void MarketMakerAnalyzer_ValidateIntegrationMethods()
        {
            // Arrange
            var testBar = 250;
            var withdrawalTimestamp = DateTime.UtcNow.AddMilliseconds(-100); // Fast quote change

            // Act
            var result = _analyzer.CalculateMMWithdrawalSignal(testBar, 2499.50m, 2500.50m, 1000m, 1000m, withdrawalTimestamp);
            var withdrawalSignal = _analyzer.GetWithdrawalSignal(testBar);
            var hasWithdrawal = _analyzer.HasWithdrawalSignal(testBar);

            // Assert
            Assert.AreEqual(result.LifespanReduction, withdrawalSignal, "GetWithdrawalSignal should match result");
            Assert.AreEqual(result.HasWithdrawalSignal, hasWithdrawal, "HasWithdrawalSignal should match result");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generate known MM behavior scenarios for accuracy testing
        /// </summary>
        private List<MMScenario> GenerateKnownMMScenarios()
        {
            var scenarios = new List<MMScenario>();

            // High withdrawal scenario
            scenarios.Add(new MMScenario
            {
                Bar = 250,
                BidPrice = 2499.50m,
                AskPrice = 2500.50m,
                BidVolume = 1000m,
                AskVolume = 1000m,
                Timestamp = DateTime.UtcNow.AddMilliseconds(-50), // Very fast quote change
                ExpectedWithdrawal = true,
                Description = "Fast quote change indicating MM withdrawal"
            });

            // Normal scenario
            scenarios.Add(new MMScenario
            {
                Bar = 260,
                BidPrice = 2499.50m,
                AskPrice = 2500.50m,
                BidVolume = 1000m,
                AskVolume = 1000m,
                Timestamp = DateTime.UtcNow.AddSeconds(-2), // Normal quote change
                ExpectedWithdrawal = false,
                Description = "Normal quote lifespan"
            });

            // Low withdrawal scenario
            scenarios.Add(new MMScenario
            {
                Bar = 270,
                BidPrice = 2499.50m,
                AskPrice = 2500.50m,
                BidVolume = 1000m,
                AskVolume = 1000m,
                Timestamp = DateTime.UtcNow.AddMilliseconds(-300), // Moderate quote change
                ExpectedWithdrawal = false,
                Description = "Moderate quote change (below threshold)"
            });

            return scenarios;
        }

        #endregion

        #region Test Data Structures

        private class MMScenario
        {
            public int Bar { get; set; }
            public decimal BidPrice { get; set; }
            public decimal AskPrice { get; set; }
            public decimal BidVolume { get; set; }
            public decimal AskVolume { get; set; }
            public DateTime Timestamp { get; set; }
            public bool ExpectedWithdrawal { get; set; }
            public string Description { get; set; }
        }

        #endregion
    }
}
