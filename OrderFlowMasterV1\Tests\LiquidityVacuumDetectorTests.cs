using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Comprehensive Unit Tests for LiquidityVacuumDetector
    /// 
    /// Validates all critical functionality according to ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md:
    /// - Performance: <0.3ms execution time
    /// - Accuracy: >90% vacuum detection accuracy
    /// - Data Compliance: 100% real live Level2 data validation
    /// - ATAS Compliance: Order book data integration
    /// - Threshold: 85% vacuum threshold validation
    /// </summary>
    [TestClass]
    public class LiquidityVacuumDetectorTests
    {
        private LiquidityVacuumDetector _detector;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = false
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                CurrentVolumeThreshold = 1000m,
                CurrentDeltaThreshold = 500m
            };

            _mockStrategy = new MockChartStrategy();
            _detector = new LiquidityVacuumDetector(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _detector?.Dispose();
        }

        #region Performance Validation Tests

        /// <summary>
        /// Test 1.1: Validate execution time <0.3ms per calculation
        /// CRITICAL: Must meet performance target for Level2 data processing
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidatePerformanceTarget()
        {
            // Arrange
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateRealisticOrderBook();
            var currentPrice = 2500m;

            // Act
            var startTime = DateTime.UtcNow;
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 100);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(executionTime < 0.3, 
                $"Execution time {executionTime:F3}ms exceeds 0.3ms target");
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(100, result.Bar, "Bar index should match");
        }

        /// <summary>
        /// Test 1.2: Validate vacuum detection accuracy >90%
        /// Uses known vacuum scenarios from realistic order book data
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateVacuumDetectionAccuracy()
        {
            // Arrange
            var vacuumScenarios = GenerateKnownVacuumScenarios();
            int correctDetections = 0;
            int totalScenarios = vacuumScenarios.Count;

            // Act
            foreach (var scenario in vacuumScenarios)
            {
                var result = _detector.DetectLiquidityVacuum(
                    scenario.Price, scenario.BidLevels, scenario.AskLevels,
                    scenario.BidVolumes, scenario.AskVolumes, scenario.Bar);
                
                if (result.HasLiquidityVacuum == scenario.ExpectedVacuum)
                {
                    correctDetections++;
                }
            }

            // Assert
            var accuracy = (decimal)correctDetections / totalScenarios;
            Assert.IsTrue(accuracy > 0.90m, 
                $"Vacuum detection accuracy {accuracy:P} must be >90% (got {correctDetections}/{totalScenarios})");
        }

        #endregion

        #region Vacuum Threshold Tests

        /// <summary>
        /// Test 1.3: Validate 85% vacuum threshold detection
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateVacuumThreshold()
        {
            // Arrange - Create scenario with exactly 85% vacuum (8.5 out of 10 levels empty)
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateVacuumScenario(0.85m);
            var currentPrice = 2500m;

            // Act
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 150);

            // Assert
            Assert.IsTrue(result.OverallVacuumRisk >= 0.85m, 
                $"Vacuum risk {result.OverallVacuumRisk:P} should be >=85% threshold");
            Assert.IsTrue(result.HasLiquidityVacuum, "Should detect liquidity vacuum at 85% threshold");
            Assert.IsTrue(result.VacuumStrength > 0.8m, "Vacuum strength should be significant");
        }

        /// <summary>
        /// Test 1.4: Validate significant vacuum detection (90% threshold)
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateSignificantVacuum()
        {
            // Arrange - Create scenario with 95% vacuum
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateVacuumScenario(0.95m);
            var currentPrice = 2500m;

            // Act
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 200);

            // Assert
            Assert.IsTrue(result.IsSignificantVacuum, "Should detect significant vacuum at 95%");
            Assert.IsTrue(result.HasLiquidityVacuum, "Should detect liquidity vacuum");
            Assert.IsTrue(result.VacuumStrength > 0.9m, "Vacuum strength should be very high");
        }

        #endregion

        #region Direction Detection Tests

        /// <summary>
        /// Test 1.5: Validate vacuum direction detection
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateDirectionDetection()
        {
            // Arrange - Create bid-side vacuum scenario
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateDirectionalVacuum(true); // Bid side vacuum
            var currentPrice = 2500m;

            // Act
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 250);

            // Assert
            Assert.AreEqual(VacuumDirection.BidSide, result.VacuumDirection, "Should detect bid-side vacuum");
            Assert.IsTrue(result.BidVacuumRisk > result.AskVacuumRisk, "Bid vacuum risk should be higher");
        }

        #endregion

        #region Real Data Compliance Tests

        /// <summary>
        /// Test 1.6: Validate 100% real live Level2 data compliance
        /// CRITICAL: Must reject synthetic/mock Level2 data and trigger emergency shutdown
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void LiquidityVacuumDetector_RejectSyntheticLevel2Data()
        {
            // Arrange - Generate obvious synthetic Level2 data pattern
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateSyntheticLevel2Data();
            var currentPrice = 2500m;

            // Act - Should trigger emergency shutdown
            _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 300);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        /// <summary>
        /// Test 1.7: Validate order book data integrity checks
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateDataIntegrity()
        {
            // Arrange - Invalid order book data (null arrays)
            decimal[] bidLevels = null;
            decimal[] askLevels = null;
            decimal[] bidVolumes = null;
            decimal[] askVolumes = null;
            var currentPrice = 2500m;

            // Act
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, 350);

            // Assert
            Assert.AreEqual(LiquidityVacuumResult.Empty.Bar, result.Bar, "Should return empty result for invalid data");
        }

        #endregion

        #region Integration Tests

        /// <summary>
        /// Test 1.8: Validate integration methods
        /// </summary>
        [TestMethod]
        public void LiquidityVacuumDetector_ValidateIntegrationMethods()
        {
            // Arrange
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateVacuumScenario(0.90m);
            var currentPrice = 2500m;
            var testBar = 400;

            // Act
            var result = _detector.DetectLiquidityVacuum(currentPrice, bidLevels, askLevels, bidVolumes, askVolumes, testBar);
            var vacuumRisk = _detector.GetVacuumRisk(testBar);
            var hasVacuum = _detector.HasLiquidityVacuum(testBar);

            // Assert
            Assert.AreEqual(result.OverallVacuumRisk, vacuumRisk, "GetVacuumRisk should match result");
            Assert.AreEqual(result.HasLiquidityVacuum, hasVacuum, "HasLiquidityVacuum should match result");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generate realistic order book data for testing
        /// </summary>
        private (decimal[] bidLevels, decimal[] askLevels, decimal[] bidVolumes, decimal[] askVolumes) GenerateRealisticOrderBook()
        {
            var random = new Random(42); // Fixed seed for reproducible tests
            var currentPrice = 2500m;
            var tickSize = 0.01m;

            // Generate 10 levels each side
            var bidLevels = new decimal[10];
            var askLevels = new decimal[10];
            var bidVolumes = new decimal[10];
            var askVolumes = new decimal[10];

            // Generate bid levels (descending from current price)
            for (int i = 0; i < 10; i++)
            {
                bidLevels[i] = currentPrice - (i + 1) * tickSize;
                bidVolumes[i] = 500m + (decimal)(random.NextDouble() * 1000); // 500-1500 volume
            }

            // Generate ask levels (ascending from current price)
            for (int i = 0; i < 10; i++)
            {
                askLevels[i] = currentPrice + (i + 1) * tickSize;
                askVolumes[i] = 500m + (decimal)(random.NextDouble() * 1000); // 500-1500 volume
            }

            return (bidLevels, askLevels, bidVolumes, askVolumes);
        }

        /// <summary>
        /// Generate vacuum scenario with specified vacuum percentage
        /// </summary>
        private (decimal[] bidLevels, decimal[] askLevels, decimal[] bidVolumes, decimal[] askVolumes) GenerateVacuumScenario(decimal vacuumPercentage)
        {
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateRealisticOrderBook();
            
            // Create vacuum by setting volumes to very low values
            int levelsToEmpty = (int)(10 * vacuumPercentage);
            
            for (int i = 0; i < levelsToEmpty; i++)
            {
                bidVolumes[i] = 50m; // Below 10% of normal (1000 * 0.1 = 100)
                askVolumes[i] = 50m;
            }

            return (bidLevels, askLevels, bidVolumes, askVolumes);
        }

        /// <summary>
        /// Generate directional vacuum scenario
        /// </summary>
        private (decimal[] bidLevels, decimal[] askLevels, decimal[] bidVolumes, decimal[] askVolumes) GenerateDirectionalVacuum(bool bidSideVacuum)
        {
            var (bidLevels, askLevels, bidVolumes, askVolumes) = GenerateRealisticOrderBook();
            
            if (bidSideVacuum)
            {
                // Create vacuum on bid side only
                for (int i = 0; i < 8; i++)
                {
                    bidVolumes[i] = 50m; // Low liquidity
                }
            }
            else
            {
                // Create vacuum on ask side only
                for (int i = 0; i < 8; i++)
                {
                    askVolumes[i] = 50m; // Low liquidity
                }
            }

            return (bidLevels, askLevels, bidVolumes, askVolumes);
        }

        /// <summary>
        /// Generate known vacuum scenarios for accuracy testing
        /// </summary>
        private List<VacuumScenario> GenerateKnownVacuumScenarios()
        {
            var scenarios = new List<VacuumScenario>();

            // High vacuum scenario
            var (bidLevels1, askLevels1, bidVolumes1, askVolumes1) = GenerateVacuumScenario(0.90m);
            scenarios.Add(new VacuumScenario
            {
                Bar = 100,
                Price = 2500m,
                BidLevels = bidLevels1,
                AskLevels = askLevels1,
                BidVolumes = bidVolumes1,
                AskVolumes = askVolumes1,
                ExpectedVacuum = true,
                Description = "90% vacuum scenario"
            });

            // Low vacuum scenario
            var (bidLevels2, askLevels2, bidVolumes2, askVolumes2) = GenerateVacuumScenario(0.30m);
            scenarios.Add(new VacuumScenario
            {
                Bar = 200,
                Price = 2500m,
                BidLevels = bidLevels2,
                AskLevels = askLevels2,
                BidVolumes = bidVolumes2,
                AskVolumes = askVolumes2,
                ExpectedVacuum = false,
                Description = "30% vacuum scenario (below threshold)"
            });

            return scenarios;
        }

        /// <summary>
        /// Generate obvious synthetic Level2 data pattern for compliance testing
        /// </summary>
        private (decimal[] bidLevels, decimal[] askLevels, decimal[] bidVolumes, decimal[] askVolumes) GenerateSyntheticLevel2Data()
        {
            var bidLevels = new decimal[10];
            var askLevels = new decimal[10];
            var bidVolumes = new decimal[10];
            var askVolumes = new decimal[10];

            // Create identical spreads (common in synthetic data)
            var basePrice = 2500m;
            var identicalSpread = 0.01m;

            for (int i = 0; i < 10; i++)
            {
                bidLevels[i] = basePrice - (i + 1) * identicalSpread; // Identical spreads
                askLevels[i] = basePrice + (i + 1) * identicalSpread; // Identical spreads
                bidVolumes[i] = 1000m; // Perfect round numbers
                askVolumes[i] = 1000m; // Perfect round numbers
            }

            return (bidLevels, askLevels, bidVolumes, askVolumes);
        }

        #endregion

        #region Test Data Structures

        private class VacuumScenario
        {
            public int Bar { get; set; }
            public decimal Price { get; set; }
            public decimal[] BidLevels { get; set; }
            public decimal[] AskLevels { get; set; }
            public decimal[] BidVolumes { get; set; }
            public decimal[] AskVolumes { get; set; }
            public bool ExpectedVacuum { get; set; }
            public string Description { get; set; }
        }

        #endregion
    }
}
