using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ATAS.Indicators;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;
using OrderFlowMasterV1.Utils;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Unit Tests for PriceAccelerationPredictor - Phase 2 Implementation
    /// 
    /// Tests velocity and acceleration calculations for 10-30 second price movement prediction
    /// with <0.8ms execution time for ultra-high-frequency scalping
    /// </summary>
    [TestClass]
    public class PriceAccelerationPredictorTests
    {
        private PriceAccelerationPredictor _predictor;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            _config = new OrderFlowConfiguration
            {
                EnableDebugLogging = false // Disable for tests
            };

            _assetConfig = new AssetConfiguration();
            _mockStrategy = new MockChartStrategy();

            _predictor = new PriceAccelerationPredictor(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _predictor?.Dispose();
        }

        [TestMethod]
        public void PredictImmediatePriceJump_WithUltraPrecisionSignal_ShouldGenerateValidPrediction()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var ultraSignal = CreateTestUltraPrecisionSignal(0.96m, SignalDirection.Long, 1.8m);
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m); // Upward trend
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Act
            var startTime = DateTime.UtcNow;
            var result = _predictor.PredictImmediatePriceJump(bar, candle, ultraSignal, priceData, volumeData);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsNotNull(result, "Prediction result should not be null");
            Assert.IsTrue(result.IsValid, "Prediction should be valid with ultra-precision signal");
            Assert.IsTrue(result.Confidence >= 0.75m, $"Confidence should be >=75%, actual: {result.Confidence:P2}");
            Assert.AreEqual(SignalDirection.Long, result.Direction, "Direction should match ultra-precision signal");
            Assert.IsTrue(result.PredictionTimingSeconds >= 10 && result.PredictionTimingSeconds <= 30, 
                $"Prediction timing should be 10-30 seconds, actual: {result.PredictionTimingSeconds}s");
            Assert.IsTrue(executionTime < 0.8, $"Execution time should be <0.8ms, actual: {executionTime:F2}ms");
            Assert.AreEqual("UltraPrecisionEnhanced", result.PredictionMethod, "Should use ultra-precision enhanced method");
        }

        [TestMethod]
        public void PredictImmediatePriceJump_WithVelocityAccelerationOnly_ShouldGenerateBasicPrediction()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var priceData = CreateTestPriceDataSeries(bar, 2490m, 2502m); // Strong upward velocity
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Act
            var result = _predictor.PredictImmediatePriceJump(bar, candle, null, priceData, volumeData);

            // Assert
            Assert.IsNotNull(result, "Prediction result should not be null");
            
            if (result.IsValid) // Only assert if prediction is valid (depends on velocity threshold)
            {
                Assert.IsTrue(result.Confidence >= 0.1m, "Confidence should be positive for valid prediction");
                Assert.AreEqual("VelocityAcceleration", result.PredictionMethod, "Should use velocity-acceleration method");
                Assert.IsTrue(result.PredictionTimingSeconds >= 10 && result.PredictionTimingSeconds <= 30, 
                    $"Prediction timing should be 10-30 seconds, actual: {result.PredictionTimingSeconds}s");
            }
        }

        [TestMethod]
        public void PredictImmediatePriceJump_WithLowVelocity_ShouldNotGenerateValidPrediction()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2500.1m, 2499.9m, 2500m, 100m); // Very low volatility
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2500m); // No velocity
            var volumeData = CreateTestVolumeDataSeries(bar, 100m);

            // Act
            var result = _predictor.PredictImmediatePriceJump(bar, candle, null, priceData, volumeData);

            // Assert
            Assert.IsNotNull(result, "Prediction result should not be null");
            Assert.IsFalse(result.IsValid, "Prediction should not be valid with low velocity");
            Assert.IsTrue(result.Confidence < 0.75m, "Confidence should be low for invalid prediction");
        }

        [TestMethod]
        public void PredictImmediatePriceJump_PerformanceTest_ShouldMeetTargets()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var ultraSignal = CreateTestUltraPrecisionSignal(0.96m, SignalDirection.Long, 1.8m);
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            var executionTimes = new List<double>();

            // Act - Run multiple iterations to test performance consistency
            for (int i = 0; i < 100; i++)
            {
                var startTime = DateTime.UtcNow;
                var result = _predictor.PredictImmediatePriceJump(bar + i, candle, ultraSignal, priceData, volumeData);
                var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                executionTimes.Add(executionTime);
            }

            // Assert
            var avgExecutionTime = executionTimes.Average();
            var maxExecutionTime = executionTimes.Max();

            Assert.IsTrue(avgExecutionTime < 0.8, $"Average execution time should be <0.8ms, actual: {avgExecutionTime:F2}ms");
            Assert.IsTrue(maxExecutionTime < 1.5, $"Maximum execution time should be <1.5ms, actual: {maxExecutionTime:F2}ms");
        }

        [TestMethod]
        public void ValidatePredictionAccuracy_WithCorrectPrediction_ShouldReturnTrue()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var ultraSignal = CreateTestUltraPrecisionSignal(0.96m, SignalDirection.Long, 1.8m);
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Generate prediction
            var prediction = _predictor.PredictImmediatePriceJump(bar, candle, ultraSignal, priceData, volumeData);
            
            // Simulate actual price close to prediction
            var actualPrice = prediction.PredictedPrice + 0.5m; // Within tolerance

            // Act
            var isAccurate = _predictor.ValidatePredictionAccuracy(bar, actualPrice, 0.001m); // 0.1% tolerance

            // Assert
            if (prediction.IsValid)
            {
                Assert.IsTrue(isAccurate, "Prediction should be validated as accurate when within tolerance");
            }
        }

        [TestMethod]
        public void ValidatePredictionAccuracy_WithIncorrectPrediction_ShouldReturnFalse()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var ultraSignal = CreateTestUltraPrecisionSignal(0.96m, SignalDirection.Long, 1.8m);
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Generate prediction
            var prediction = _predictor.PredictImmediatePriceJump(bar, candle, ultraSignal, priceData, volumeData);
            
            // Simulate actual price far from prediction
            var actualPrice = prediction.PredictedPrice + 50m; // Way outside tolerance

            // Act
            var isAccurate = _predictor.ValidatePredictionAccuracy(bar, actualPrice, 0.001m); // 0.1% tolerance

            // Assert
            if (prediction.IsValid)
            {
                Assert.IsFalse(isAccurate, "Prediction should be validated as inaccurate when outside tolerance");
            }
        }

        [TestMethod]
        public void GetPredictionStatistics_AfterMultiplePredictions_ShouldProvideAccurateMetrics()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var ultraSignal = CreateTestUltraPrecisionSignal(0.96m, SignalDirection.Long, 1.8m);
            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Act - Generate multiple predictions
            for (int i = 0; i < 10; i++)
            {
                _predictor.PredictImmediatePriceJump(bar + i, candle, ultraSignal, priceData, volumeData);
            }

            var stats = _predictor.GetPredictionStatistics();

            // Assert
            Assert.IsNotNull(stats, "Statistics should not be null");
            Assert.AreEqual(10, stats.TotalCalculations, "Should track total calculations");
            Assert.IsTrue(stats.AverageExecutionTimeMs < 0.8, $"Average execution time should be <0.8ms, actual: {stats.AverageExecutionTimeMs:F2}ms");
            Assert.IsTrue(stats.PredictionAccuracy >= 0, "Prediction accuracy should be non-negative");
            Assert.AreEqual(0.8m, stats.PerformanceTarget, "Performance target should be 0.8ms");
        }

        [TestMethod]
        public void PredictImmediatePriceJump_WithRealDataValidation_ShouldEnforceCompliance()
        {
            // Arrange
            var bar = 100;
            var oldCandle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            oldCandle.Time = DateTime.UtcNow.AddMinutes(-10); // Old data

            var priceData = CreateTestPriceDataSeries(bar, 2500m, 2502m);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
            {
                _predictor.PredictImmediatePriceJump(bar, oldCandle, null, priceData, volumeData);
            }, "Should throw exception for non-live data");
        }

        #region Helper Methods

        private IndicatorCandle CreateTestCandle(decimal open, decimal high, decimal low, decimal close, decimal volume)
        {
            return new IndicatorCandle
            {
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Time = DateTime.UtcNow
            };
        }

        private UltraPrecisionSignal CreateTestUltraPrecisionSignal(decimal confidence, SignalDirection direction, decimal strength)
        {
            return new UltraPrecisionSignal
            {
                IsValid = true,
                Confidence = confidence,
                Direction = direction,
                Strength = strength,
                Analysis = "Test ultra-precision signal"
            };
        }

        private IDataSeries<decimal> CreateTestPriceDataSeries(int currentBar, decimal startPrice, decimal endPrice)
        {
            var series = new ValueDataSeries("TestPrice");
            
            // Create price trend from start to end
            var priceStep = (endPrice - startPrice) / 60; // 60 bars of data
            
            for (int i = Math.Max(0, currentBar - 60); i <= currentBar; i++)
            {
                var barOffset = i - (currentBar - 60);
                series[i] = startPrice + (priceStep * barOffset);
            }

            return series;
        }

        private IDataSeries<decimal> CreateTestVolumeDataSeries(int currentBar, decimal volume)
        {
            var series = new ValueDataSeries("TestVolume");
            
            for (int i = Math.Max(0, currentBar - 60); i <= currentBar; i++)
            {
                series[i] = volume;
            }

            return series;
        }

        #endregion
    }
}
