using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Unit Tests for UltraPrecisionRiskManager - Phase 2 Implementation
    /// 
    /// Tests confidence-based dynamic stop placement system with 80% stop hit reduction
    /// using confluence scoring with <0.5ms execution time
    /// </summary>
    [TestClass]
    public class UltraPrecisionRiskManagerTests
    {
        private UltraPrecisionRiskManager _riskManager;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            _config = new OrderFlowConfiguration
            {
                EnableDebugLogging = false // Disable for tests
            };

            _assetConfig = new AssetConfiguration();
            _mockStrategy = new MockChartStrategy();

            _riskManager = new UltraPrecisionRiskManager(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _riskManager?.Dispose();
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithUltraHighConfidence_ShouldUseTightestStops()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var ultraHighConfidence = 0.96m; // >95%
            var normalVolatility = 0.01m; // 1%
            var ultraSignal = CreateTestUltraPrecisionSignal(ultraHighConfidence, SignalDirection.Long);

            // Act
            var startTime = DateTime.UtcNow;
            var result = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, ultraHighConfidence, 
                normalVolatility, ultraSignal);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsNotNull(result, "Stop result should not be null");
            Assert.IsTrue(result.OptimalStopDistance <= 0.003m, $"Ultra-high confidence should use ≤0.30% stops, actual: {result.OptimalStopDistance:P3}");
            Assert.AreEqual(RiskLevel.UltraLow, result.RiskLevel, "Risk level should be UltraLow for ultra-high confidence");
            Assert.IsTrue(result.StopHitReduction >= 0.8m, $"Should achieve ≥80% stop hit reduction, actual: {result.StopHitReduction:P2}");
            Assert.IsTrue(executionTime < 0.5, $"Execution time should be <0.5ms, actual: {executionTime:F2}ms");
            Assert.IsTrue(result.OptimalStopPrice > 0, "Stop price should be calculated");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithHighConfidence_ShouldUseModerateStops()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var highConfidence = 0.92m; // 90-95%
            var normalVolatility = 0.01m;

            // Act
            var result = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, highConfidence, normalVolatility);

            // Assert
            Assert.IsNotNull(result, "Stop result should not be null");
            Assert.IsTrue(result.OptimalStopDistance > 0.003m && result.OptimalStopDistance <= 0.0036m, 
                $"High confidence should use 0.30-0.36% stops, actual: {result.OptimalStopDistance:P3}");
            Assert.AreEqual(RiskLevel.Low, result.RiskLevel, "Risk level should be Low for high confidence");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithMediumConfidence_ShouldUseStandardStops()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var mediumConfidence = 0.87m; // 85-90%
            var normalVolatility = 0.01m;

            // Act
            var result = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, mediumConfidence, normalVolatility);

            // Assert
            Assert.IsNotNull(result, "Stop result should not be null");
            Assert.IsTrue(result.OptimalStopDistance > 0.0036m && result.OptimalStopDistance <= 0.0045m, 
                $"Medium confidence should use 0.36-0.45% stops, actual: {result.OptimalStopDistance:P3}");
            Assert.AreEqual(RiskLevel.Medium, result.RiskLevel, "Risk level should be Medium for medium confidence");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithLowConfidence_ShouldUseWiderStops()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var lowConfidence = 0.70m; // <85%
            var normalVolatility = 0.01m;

            // Act
            var result = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, lowConfidence, normalVolatility);

            // Assert
            Assert.IsNotNull(result, "Stop result should not be null");
            Assert.IsTrue(result.OptimalStopDistance > 0.0045m, $"Low confidence should use >0.45% stops, actual: {result.OptimalStopDistance:P3}");
            Assert.IsTrue(result.RiskLevel == RiskLevel.High || result.RiskLevel == RiskLevel.VeryHigh, 
                "Risk level should be High or VeryHigh for low confidence");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithHighVolatility_ShouldAdjustStopsUpward()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var highVolatility = 0.03m; // 3% - high volatility

            // Act
            var normalVolResult = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, confidence, 0.01m);
            var highVolResult = _riskManager.CalculateOptimalStopDistance(bar + 1, currentPrice, confidence, highVolatility);

            // Assert
            Assert.IsTrue(highVolResult.OptimalStopDistance > normalVolResult.OptimalStopDistance, 
                "High volatility should increase stop distance");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithUltraPrecisionSignal_ShouldApplySignalAdjustment()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var normalVolatility = 0.01m;
            var ultraSignal = CreateTestUltraPrecisionSignal(confidence, SignalDirection.Long, 2.0m); // Strong signal
            var pricePrediction = CreateTestPricePrediction(15); // Fast prediction

            // Act
            var baseResult = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, confidence, normalVolatility);
            var enhancedResult = _riskManager.CalculateOptimalStopDistance(bar + 1, currentPrice, confidence, 
                normalVolatility, ultraSignal, pricePrediction);

            // Assert
            Assert.IsTrue(enhancedResult.OptimalStopDistance <= baseResult.OptimalStopDistance, 
                "Ultra-precision signal should allow tighter stops");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_PerformanceTest_ShouldMeetTargets()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var volatility = 0.01m;

            var executionTimes = new List<double>();

            // Act - Run multiple iterations to test performance consistency
            for (int i = 0; i < 100; i++)
            {
                var startTime = DateTime.UtcNow;
                var result = _riskManager.CalculateOptimalStopDistance(bar + i, currentPrice, confidence, volatility);
                var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                executionTimes.Add(executionTime);
            }

            // Assert
            var avgExecutionTime = executionTimes.Average();
            var maxExecutionTime = executionTimes.Max();

            Assert.IsTrue(avgExecutionTime < 0.5, $"Average execution time should be <0.5ms, actual: {avgExecutionTime:F2}ms");
            Assert.IsTrue(maxExecutionTime < 1.0, $"Maximum execution time should be <1.0ms, actual: {maxExecutionTime:F2}ms");
        }

        [TestMethod]
        public void ValidateStopEffectiveness_WithEffectiveStop_ShouldReturnTrue()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var volatility = 0.01m;

            // Calculate stop
            var stopResult = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, confidence, volatility);
            
            // Simulate stop not being hit
            var actualExitPrice = currentPrice + 5m; // Profitable exit

            // Act
            var isEffective = _riskManager.ValidateStopEffectiveness(bar, false, actualExitPrice);

            // Assert
            Assert.IsTrue(isEffective, "Stop should be effective when not hit");
        }

        [TestMethod]
        public void ValidateStopEffectiveness_WithIneffectiveStop_ShouldReturnFalse()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var volatility = 0.01m;

            // Calculate stop
            var stopResult = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, confidence, volatility);
            
            // Simulate stop being hit far from optimal price
            var actualExitPrice = stopResult.OptimalStopPrice - 10m; // Much worse than expected

            // Act
            var isEffective = _riskManager.ValidateStopEffectiveness(bar, true, actualExitPrice);

            // Assert
            Assert.IsFalse(isEffective, "Stop should be ineffective when hit far from optimal price");
        }

        [TestMethod]
        public void GetRiskStatistics_AfterMultipleCalculations_ShouldProvideAccurateMetrics()
        {
            // Arrange
            var currentPrice = 2500m;
            var confidence = 0.96m;
            var volatility = 0.01m;

            // Act - Generate multiple stop calculations
            for (int i = 0; i < 10; i++)
            {
                _riskManager.CalculateOptimalStopDistance(100 + i, currentPrice, confidence, volatility);
            }

            var stats = _riskManager.GetRiskStatistics();

            // Assert
            Assert.IsNotNull(stats, "Statistics should not be null");
            Assert.AreEqual(10, stats.TotalCalculations, "Should track total calculations");
            Assert.AreEqual(10, stats.TotalStopCalculations, "Should track stop calculations");
            Assert.IsTrue(stats.AverageExecutionTimeMs < 0.5, $"Average execution time should be <0.5ms, actual: {stats.AverageExecutionTimeMs:F2}ms");
            Assert.AreEqual(0.8m, stats.ReductionTarget, "Reduction target should be 80%");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_WithRealDataValidation_ShouldEnforceCompliance()
        {
            // Arrange
            var bar = 100;
            var invalidPrice = -100m; // Invalid price
            var confidence = 0.96m;
            var volatility = 0.01m;

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
            {
                _riskManager.CalculateOptimalStopDistance(bar, invalidPrice, confidence, volatility);
            }, "Should throw exception for invalid data");
        }

        [TestMethod]
        public void CalculateOptimalStopDistance_StopHitReductionTarget_ShouldAchieve80PercentReduction()
        {
            // Arrange
            var bar = 100;
            var currentPrice = 2500m;
            var ultraHighConfidence = 0.97m; // Very high confidence
            var lowVolatility = 0.005m; // Low volatility
            var ultraSignal = CreateTestUltraPrecisionSignal(ultraHighConfidence, SignalDirection.Long, 2.5m);
            var fastPrediction = CreateTestPricePrediction(12); // Fast prediction

            // Act
            var result = _riskManager.CalculateOptimalStopDistance(bar, currentPrice, ultraHighConfidence, 
                lowVolatility, ultraSignal, fastPrediction);

            // Assert
            Assert.IsTrue(result.StopHitReduction >= 0.8m, 
                $"Should achieve ≥80% stop hit reduction with optimal conditions, actual: {result.StopHitReduction:P2}");
        }

        #region Helper Methods

        private UltraPrecisionSignal CreateTestUltraPrecisionSignal(decimal confidence, SignalDirection direction, decimal strength = 1.5m)
        {
            return new UltraPrecisionSignal
            {
                IsValid = true,
                Confidence = confidence,
                Direction = direction,
                Strength = strength,
                Analysis = "Test ultra-precision signal"
            };
        }

        private PricePredictionResult CreateTestPricePrediction(int timingSeconds)
        {
            return new PricePredictionResult
            {
                IsValid = true,
                Confidence = 0.85m,
                PredictionTimingSeconds = timingSeconds,
                Direction = SignalDirection.Long,
                PredictionMethod = "UltraPrecisionEnhanced"
            };
        }

        #endregion
    }
}
