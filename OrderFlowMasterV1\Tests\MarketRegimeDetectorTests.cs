using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using ATAS.Indicators;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 PHASE 3: Unit tests for MarketRegimeDetector
    /// 
    /// Tests cover:
    /// - Volatility regime classification (Low/Medium/High/Extreme)
    /// - Trend regime detection
    /// - Market phase identification
    /// - Regime change detection
    /// - Performance targets (<1ms execution time)
    /// </summary>
    [TestClass]
    public class MarketRegimeDetectorTests
    {
        private TestChartStrategy _mockStrategy;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MarketRegimeDetector _detector;

        [TestInitialize]
        public void Setup()
        {
            _mockStrategy = new TestChartStrategy();
            _config = new OrderFlowConfiguration();
            _assetConfig = new AssetConfiguration();
            _detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _detector?.Dispose();
        }

        [TestMethod]
        public void Constructor_ValidParameters_InitializesSuccessfully()
        {
            // Arrange & Act
            using var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);

            // Assert
            Assert.IsNotNull(detector);
            Assert.AreEqual(VolatilityRegime.Unknown, detector.CurrentVolatilityRegime);
            Assert.AreEqual(TrendRegime.Unknown, detector.CurrentTrendRegime);
            Assert.AreEqual(MarketPhase.Unknown, detector.CurrentMarketPhase);
            Assert.AreEqual(0, detector.RegimeDetectionCount);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_NullStrategy_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            new MarketRegimeDetector(null, _config, _assetConfig);
        }

        [TestMethod]
        public void AnalyzeMarketRegime_InsufficientData_ReturnsUnknownRegimes()
        {
            // Arrange
            var candle = CreateTestCandle(100m, 105m, 95m, 102m, 1000m);

            // Act
            var result = _detector.AnalyzeMarketRegime(10, 102m, 1000m, candle); // Bar 10 < lookback period

            // Assert
            Assert.AreEqual(VolatilityRegime.Unknown, result.VolatilityRegime);
            Assert.AreEqual(TrendRegime.Unknown, result.TrendRegime);
            Assert.AreEqual(MarketPhase.Unknown, result.MarketPhase);
            Assert.AreEqual(0m, result.Confidence);
        }

        [TestMethod]
        public void AnalyzeMarketRegime_HighVolatilityCandle_DetectsHighVolatility()
        {
            // Arrange - Create high volatility scenario
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Simulate sufficient historical data by calling multiple times
            for (int i = 0; i < 60; i++)
            {
                var volatility = i < 30 ? 0.01m : 0.05m; // Low then high volatility
                var range = volatility * 100m;
                var candle = CreateTestCandle(100m, 100m + range, 100m - range, 100m, 1000m);
                detector.AnalyzeMarketRegime(i, 100m, 1000m, candle);
            }

            // Act - Analyze with very high volatility
            var highVolCandle = CreateTestCandle(100m, 120m, 80m, 110m, 2000m); // 40% range
            var result = detector.AnalyzeMarketRegime(60, 110m, 2000m, highVolCandle);

            // Assert
            Assert.IsTrue(result.VolatilityRegime == VolatilityRegime.High || 
                         result.VolatilityRegime == VolatilityRegime.Extreme);
            Assert.IsTrue(result.ExecutionTimeMs < 1.0m, $"Execution time {result.ExecutionTimeMs}ms exceeds 1ms target");

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void AnalyzeMarketRegime_LowVolatilityCandle_DetectsLowVolatility()
        {
            // Arrange - Create low volatility scenario
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Simulate sufficient historical data with varying volatility
            for (int i = 0; i < 60; i++)
            {
                var volatility = i < 30 ? 0.05m : 0.01m; // High then low volatility
                var range = volatility * 100m;
                var candle = CreateTestCandle(100m, 100m + range, 100m - range, 100m, 1000m);
                detector.AnalyzeMarketRegime(i, 100m, 1000m, candle);
            }

            // Act - Analyze with very low volatility
            var lowVolCandle = CreateTestCandle(100m, 101m, 99m, 100.5m, 800m); // 2% range
            var result = detector.AnalyzeMarketRegime(60, 100.5m, 800m, lowVolCandle);

            // Assert
            Assert.IsTrue(result.VolatilityRegime == VolatilityRegime.Low || 
                         result.VolatilityRegime == VolatilityRegime.VeryLow);
            Assert.IsTrue(result.ExecutionTimeMs < 1.0m);

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void AnalyzeMarketRegime_TrendingMarket_DetectsTrendRegime()
        {
            // Arrange - Create trending scenario
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Simulate uptrending market
            for (int i = 0; i < 60; i++)
            {
                var price = 100m + i * 0.5m; // Steady uptrend
                var candle = CreateTestCandle(price - 0.5m, price + 1m, price - 1m, price, 1000m);
                detector.AnalyzeMarketRegime(i, price, 1000m, candle);
            }

            // Act
            var trendCandle = CreateTestCandle(129m, 131m, 128m, 130m, 1200m);
            var result = detector.AnalyzeMarketRegime(60, 130m, 1200m, trendCandle);

            // Assert
            Assert.IsTrue(result.TrendRegime.ToString().Contains("Uptrend"));
            Assert.IsTrue(result.ExecutionTimeMs < 1.0m);

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void AnalyzeMarketRegime_RegimeChange_DetectsChange()
        {
            // Arrange
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Establish initial regime with low volatility
            for (int i = 0; i < 60; i++)
            {
                var candle = CreateTestCandle(100m, 101m, 99m, 100m, 1000m); // Low volatility
                detector.AnalyzeMarketRegime(i, 100m, 1000m, candle);
            }

            // Wait for minimum regime duration (simulate time passage)
            System.Threading.Thread.Sleep(100);

            // Act - Introduce high volatility
            var highVolCandle = CreateTestCandle(100m, 115m, 85m, 110m, 2000m);
            var result = detector.AnalyzeMarketRegime(60, 110m, 2000m, highVolCandle);

            // Assert
            Assert.IsTrue(result.RegimeChanged || result.VolatilityRegime != VolatilityRegime.Unknown);

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void GetRegimeAdjustments_HighVolatilityRegime_ReturnsConservativeAdjustments()
        {
            // Arrange
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Force high volatility regime
            for (int i = 0; i < 60; i++)
            {
                var range = 10m; // High volatility
                var candle = CreateTestCandle(100m, 100m + range, 100m - range, 100m, 1000m);
                detector.AnalyzeMarketRegime(i, 100m, 1000m, candle);
            }

            // Act
            var adjustments = detector.GetRegimeAdjustments();

            // Assert
            Assert.IsNotNull(adjustments);
            Assert.IsNotNull(adjustments.RecommendedAdjustments);
            
            if (adjustments.RecommendedAdjustments.ContainsKey("StopLossMultiplier"))
            {
                Assert.IsTrue(adjustments.RecommendedAdjustments["StopLossMultiplier"] > 1.0m, 
                    "High volatility should recommend wider stops");
            }

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void AnalyzeMarketRegime_PerformanceTarget_MeetsExecutionTime()
        {
            // Arrange
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);
            
            // Prepare sufficient data
            for (int i = 0; i < 60; i++)
            {
                var candle = CreateTestCandle(100m, 102m, 98m, 101m, 1000m);
                detector.AnalyzeMarketRegime(i, 101m, 1000m, candle);
            }

            // Act - Measure execution time
            var testCandle = CreateTestCandle(101m, 103m, 99m, 102m, 1100m);
            var startTime = DateTime.UtcNow;
            var result = detector.AnalyzeMarketRegime(60, 102m, 1100m, testCandle);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(executionTime < 1.0, $"Execution time {executionTime}ms exceeds 1ms target");
            Assert.IsTrue(result.ExecutionTimeMs < 1.0m);

            // Cleanup
            detector.Dispose();
        }

        [TestMethod]
        public void Dispose_MultipleCallsSafe_NoExceptions()
        {
            // Arrange
            var detector = new MarketRegimeDetector(_mockStrategy, _config, _assetConfig);

            // Act & Assert - Should not throw
            detector.Dispose();
            detector.Dispose(); // Second call should be safe
        }

        #region Helper Methods

        private IndicatorCandle CreateTestCandle(decimal open, decimal high, decimal low, decimal close, decimal volume)
        {
            return new IndicatorCandle
            {
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Time = DateTime.UtcNow
            };
        }

        #endregion
    }
}
