using System;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// Unit tests for TickAnalysisEngine - Phase 3 Ultra-Precision Component
    /// Validates ultra-high-frequency tick analysis and prediction capabilities
    /// </summary>
    [TestClass]
    public class TickAnalysisEngineTests
    {
        private TickAnalysisEngine _tickAnalysisEngine;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private TestChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = true
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                TickSize = 0.01m,
                MinimumVolume = 0.001m
            };

            _mockStrategy = new TestChartStrategy();
            _tickAnalysisEngine = new TickAnalysisEngine(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _tickAnalysisEngine?.Dispose();
        }

        [TestMethod]
        public void TickAnalysisEngine_Initialization_ShouldSucceed()
        {
            // Arrange & Act - Done in Setup

            // Assert
            Assert.IsNotNull(_tickAnalysisEngine);
            Assert.IsNotNull(_config);
            Assert.IsNotNull(_assetConfig);
        }

        [TestMethod]
        public void AnalyzeTicks_WithValidCandle_ShouldReturnValidResult()
        {
            // Arrange
            var bar = 10;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 150m);
            var currentPrice = 2502m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(bar, result.Bar);
            Assert.AreEqual(currentPrice, result.CurrentPrice);
            Assert.IsTrue(result.Timestamp > DateTime.MinValue);
        }

        [TestMethod]
        public void AnalyzeTicks_WithHighVolume_ShouldDetectTickVelocitySpike()
        {
            // Arrange
            var bar = 15;
            var highVolumeCandle = CreateTestCandle(2500m, 2510m, 2495m, 2508m, 500m); // High volume
            var currentPrice = 2508m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, highVolumeCandle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.TickVelocity > 0);
            // High volume should result in higher tick velocity
            Assert.IsTrue(result.TickVelocity >= 30); // 500 volume / 15 seconds = 33.33 ticks/second
        }

        [TestMethod]
        public void AnalyzeTicks_WithPriceMovement_ShouldCalculateAcceleration()
        {
            // Arrange
            var bar = 20;
            var candle = CreateTestCandle(2500m, 2520m, 2495m, 2515m, 200m); // Significant price movement
            var currentPrice = 2515m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(Math.Abs(result.PriceAcceleration) >= 0); // Should have some acceleration
            Assert.IsTrue(Math.Abs(result.PredictedPriceMovement) > 0); // Should predict movement
        }

        [TestMethod]
        public void AnalyzeTicks_WithInstitutionalVolume_ShouldDetectInstitutionalActivity()
        {
            // Arrange
            var bar = 25;
            var institutionalCandle = CreateTestCandle(2500m, 2505m, 2498m, 2503m, 1000m); // Very high volume
            var currentPrice = 2503m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, institutionalCandle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.HasInstitutionalTicks);
            Assert.IsTrue(result.InstitutionalActivityLevel > 0);
            Assert.IsTrue(result.InstitutionalActivityLevel <= 1.0m);
        }

        [TestMethod]
        public void AnalyzeTicks_WithHighConfidenceSignal_ShouldReturnValidPrediction()
        {
            // Arrange
            var bar = 30;
            var strongSignalCandle = CreateTestCandle(2500m, 2515m, 2498m, 2512m, 800m); // Strong movement + high volume
            var currentPrice = 2512m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, strongSignalCandle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.PredictionConfidence > 0.5m);
            Assert.IsTrue(Math.Abs(result.PredictedPriceMovement) > 0);
            Assert.IsTrue(result.IsValid);
        }

        [TestMethod]
        public void GetTickPrediction_WithValidBar_ShouldReturnPrediction()
        {
            // Arrange
            var bar = 35;
            var candle = CreateTestCandle(2500m, 2510m, 2495m, 2507m, 300m);
            var currentPrice = 2507m;

            // First analyze ticks
            _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);

            // Act
            var prediction = _tickAnalysisEngine.GetTickPrediction(bar);

            // Assert
            Assert.IsTrue(Math.Abs(prediction) >= 0); // Should return some prediction value
        }

        [TestMethod]
        public void GetInstitutionalActivity_WithValidBar_ShouldReturnActivityLevel()
        {
            // Arrange
            var bar = 40;
            var candle = CreateTestCandle(2500m, 2505m, 2498m, 2503m, 600m);
            var currentPrice = 2503m;

            // First analyze ticks
            _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);

            // Act
            var activityLevel = _tickAnalysisEngine.GetInstitutionalActivity(bar);

            // Assert
            Assert.IsTrue(activityLevel >= 0);
            Assert.IsTrue(activityLevel <= 1.0m);
        }

        [TestMethod]
        public void AnalyzeTicks_PerformanceTest_ShouldExecuteWithinTimeLimit()
        {
            // Arrange
            var bar = 45;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 200m);
            var currentPrice = 2502m;
            var maxExecutionTimeMs = 1.0; // 1ms target (should be <0.8ms per spec)

            // Act
            var startTime = DateTime.UtcNow;
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(executionTime < maxExecutionTimeMs, 
                $"Execution time {executionTime:F2}ms exceeds target {maxExecutionTimeMs}ms");
        }

        [TestMethod]
        public void AnalyzeTicks_MultipleConsecutiveCalls_ShouldMaintainPerformance()
        {
            // Arrange
            var iterations = 100;
            var maxAverageTimeMs = 1.0;
            var totalTime = 0.0;

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var bar = 50 + i;
                var candle = CreateTestCandle(2500m + i, 2505m + i, 2495m + i, 2502m + i, 200m + i);
                var currentPrice = 2502m + i;

                var startTime = DateTime.UtcNow;
                var result = _tickAnalysisEngine.AnalyzeTicks(bar, candle, currentPrice);
                var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

                totalTime += executionTime;

                // Assert each result is valid
                Assert.IsNotNull(result);
            }

            // Assert average performance
            var averageTime = totalTime / iterations;
            Assert.IsTrue(averageTime < maxAverageTimeMs,
                $"Average execution time {averageTime:F2}ms exceeds target {maxAverageTimeMs}ms");
        }

        [TestMethod]
        public void AnalyzeTicks_WithNullCandle_ShouldReturnEmptyResult()
        {
            // Arrange
            var bar = 55;
            IndicatorCandle nullCandle = null;
            var currentPrice = 2500m;

            // Act
            var result = _tickAnalysisEngine.AnalyzeTicks(bar, nullCandle, currentPrice);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(TickAnalysisResult.Empty.Bar, result.Bar);
        }

        #region Helper Methods

        /// <summary>
        /// Create a test candle with specified OHLCV values
        /// </summary>
        private IndicatorCandle CreateTestCandle(decimal open, decimal high, decimal low, decimal close, decimal volume)
        {
            return new IndicatorCandle
            {
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                LastTime = DateTime.UtcNow,
                Delta = volume * 0.1m // Simplified delta calculation
            };
        }

        #endregion
    }

    /// <summary>
    /// Test implementation of ChartStrategy for unit testing
    /// </summary>
    public class TestChartStrategy : ChartStrategy
    {
        public TestChartStrategy() : base(true) { }

        protected override void OnCalculate(int bar, decimal value) { }

        public new void LogInfo(string message) => base.LogInfo(message);
        public new void LogWarn(string message) => base.LogWarn(message);
        public new void LogError(string message) => base.LogError(message);
        public new void LogDebug(string message) => base.LogDebug(message);
    }
}
