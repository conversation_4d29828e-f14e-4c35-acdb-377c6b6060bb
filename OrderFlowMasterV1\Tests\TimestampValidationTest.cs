using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ATAS.Indicators.Technical;
using ATAS.DataFeedsCore;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// Unit tests for timestamp validation fixes to prevent corrupted ATAS timestamp issues
    /// Tests the fix for the massive data age calculations (63,889,481,740.8 seconds bug)
    /// </summary>
    [TestClass]
    public class TimestampValidationTest
    {
        [TestMethod]
        public void TestCorruptedTimestampDetection()
        {
            // Test DateTime.MinValue detection
            var minValueAge = DateTime.UtcNow - DateTime.MinValue;
            Assert.IsTrue(minValueAge.TotalSeconds > 1000000000, 
                "DateTime.MinValue should produce massive data age");

            // Test DateTime.MaxValue detection  
            var maxValueAge = DateTime.MaxValue - DateTime.UtcNow;
            Assert.IsTrue(maxValueAge.TotalSeconds > 1000000000,
                "DateTime.MaxValue should produce massive future age");
        }

        [TestMethod]
        public void TestValidTimestampRange()
        {
            var now = DateTime.UtcNow;
            
            // Test valid recent timestamp (should pass)
            var recentTime = now.AddSeconds(-30);
            var recentAge = (now - recentTime).TotalSeconds;
            Assert.IsTrue(recentAge >= 0 && recentAge < 60, 
                "Recent timestamp should have reasonable age");

            // Test old timestamp (should be rejected)
            var oldTime = now.AddYears(-2);
            var oldAge = (now - oldTime).TotalSeconds;
            Assert.IsTrue(oldAge > 365 * 24 * 3600, 
                "Old timestamp should be rejected");

            // Test future timestamp (should be rejected)
            var futureTime = now.AddHours(2);
            var futureAge = (futureTime - now).TotalSeconds;
            Assert.IsTrue(futureAge > 3600, 
                "Future timestamp should be rejected");
        }

        [TestMethod]
        public void TestDataAgeCalculationSafety()
        {
            var now = DateTime.UtcNow;
            
            // Simulate the bug scenario
            var corruptedTimestamp = DateTime.MinValue;
            var massiveAge = (now - corruptedTimestamp).TotalSeconds;
            
            // This is what was causing the emergency shutdowns
            Assert.IsTrue(massiveAge > 63000000000, 
                "Corrupted timestamp produces massive age calculation");
            
            // Verify our fix would catch this
            Assert.IsTrue(corruptedTimestamp == DateTime.MinValue, 
                "Fix should detect DateTime.MinValue");
            Assert.IsTrue(corruptedTimestamp < now.AddYears(-1), 
                "Fix should detect unreasonably old timestamps");
        }

        [TestMethod]
        public void TestBybitConnectionScenario()
        {
            // Simulate the user's scenario:
            // - Bybit connection is fine (57ms latency, 18ms ago update)
            // - But some candles have corrupted timestamps
            
            var now = DateTime.UtcNow;
            
            // Good candle (like the user's connection shows)
            var goodCandle = now.AddMilliseconds(-18); // 18ms ago
            var goodAge = (now - goodCandle).TotalSeconds;
            Assert.IsTrue(goodAge < 1, "Good candle should have sub-second age");
            
            // Corrupted candle (causing the massive age calculations)
            var corruptedCandle = DateTime.MinValue;
            var corruptedAge = (now - corruptedCandle).TotalSeconds;
            Assert.IsTrue(corruptedAge > 60000000000, "Corrupted candle causes massive age");
            
            // Our fix should handle both scenarios correctly
            Assert.IsTrue(goodCandle > now.AddYears(-1), "Good candle passes validation");
            Assert.IsTrue(corruptedCandle < now.AddYears(-1), "Corrupted candle fails validation");
        }
    }
}
