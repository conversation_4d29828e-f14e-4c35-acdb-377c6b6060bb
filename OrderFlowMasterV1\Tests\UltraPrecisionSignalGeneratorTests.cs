using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;
using OrderFlowMasterV1.Utils;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Unit Tests for UltraPrecisionSignalGenerator - Phase 2 Implementation
    /// 
    /// Tests the 5-layer confluence system requiring ALL layers to align for ultra-precision signals:
    /// Layer 1: Order flow velocity spike (>5x baseline)
    /// Layer 2: Liquidity vacuum detection (>85% threshold)
    /// Layer 3: Institutional flow detection (>80% confidence)
    /// Layer 4: CVD acceleration analysis (>2x normal)
    /// Layer 5: Extreme imbalance detection (>8:1 ratio)
    /// 
    /// Target: 95-98% confidence scoring with <1ms execution time
    /// </summary>
    [TestClass]
    public class UltraPrecisionSignalGeneratorTests
    {
        private UltraPrecisionSignalGenerator _signalGenerator;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;
        private OrderFlowVelocityDetector _mockVelocityDetector;
        private LiquidityVacuumDetector _mockVacuumDetector;
        private StopHuntDetector _mockStopHuntDetector;
        private MarketMakerAnalyzer _mockMarketMakerAnalyzer;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnableDebugLogging = false, // Disable for tests
                ConfluenceThreshold = 30m
            };

            _assetConfig = new AssetConfiguration();
            _mockStrategy = new MockChartStrategy();

            // Initialize mock Phase 1 components
            _mockVelocityDetector = new OrderFlowVelocityDetector(_config, _assetConfig, _mockStrategy);
            _mockVacuumDetector = new LiquidityVacuumDetector(_config, _assetConfig, _mockStrategy);
            _mockStopHuntDetector = new StopHuntDetector(_config, _assetConfig, _mockStrategy);
            _mockMarketMakerAnalyzer = new MarketMakerAnalyzer(_config, _assetConfig, _mockStrategy);

            // Initialize UltraPrecisionSignalGenerator
            _signalGenerator = new UltraPrecisionSignalGenerator(_config, _assetConfig, _mockStrategy,
                _mockVelocityDetector, _mockVacuumDetector, _mockStopHuntDetector, _mockMarketMakerAnalyzer);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _signalGenerator?.Dispose();
            _mockVelocityDetector?.Dispose();
            _mockVacuumDetector?.Dispose();
            _mockStopHuntDetector?.Dispose();
            _mockMarketMakerAnalyzer?.Dispose();
        }

        [TestMethod]
        public void ValidateUltraPrecisionEntry_WithAllLayersAligned_ShouldGenerateValidSignal()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var footprintData = CreateTestFootprintData(highInstitutionalFlow: true, extremeImbalance: true);
            var cvdData = CreateTestCVDData(highAcceleration: true);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);
            var deltaData = CreateTestDeltaDataSeries(bar, 500m);

            // Act
            var startTime = DateTime.UtcNow;
            var result = _signalGenerator.ValidateUltraPrecisionEntry(bar, candle, footprintData, cvdData, volumeData, deltaData);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsNotNull(result, "Signal should not be null");
            Assert.IsTrue(result.IsValid, "Signal should be valid when all layers align");
            Assert.IsTrue(result.Confidence >= 0.95m, $"Confidence should be >=95%, actual: {result.Confidence:P2}");
            Assert.IsTrue(result.Confidence <= 0.98m, $"Confidence should be <=98%, actual: {result.Confidence:P2}");
            Assert.AreNotEqual(SignalDirection.Neutral, result.Direction, "Signal direction should not be neutral");
            Assert.IsTrue(result.Strength > 0, "Signal strength should be positive");
            Assert.IsTrue(executionTime < 1.0, $"Execution time should be <1ms, actual: {executionTime:F2}ms");
        }

        [TestMethod]
        public void ValidateUltraPrecisionEntry_WithMissingLayers_ShouldNotGenerateValidSignal()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var footprintData = CreateTestFootprintData(highInstitutionalFlow: false, extremeImbalance: false); // Missing layers
            var cvdData = CreateTestCVDData(highAcceleration: false); // Missing layer
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);
            var deltaData = CreateTestDeltaDataSeries(bar, 100m); // Low delta - missing velocity layer

            // Act
            var result = _signalGenerator.ValidateUltraPrecisionEntry(bar, candle, footprintData, cvdData, volumeData, deltaData);

            // Assert
            Assert.IsNotNull(result, "Signal should not be null");
            Assert.IsFalse(result.IsValid, "Signal should not be valid when layers don't align");
            Assert.IsTrue(result.Confidence < 0.95m, $"Confidence should be <95%, actual: {result.Confidence:P2}");
        }

        [TestMethod]
        public void ValidateUltraPrecisionEntry_PerformanceTest_ShouldMeetTargets()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var footprintData = CreateTestFootprintData(highInstitutionalFlow: true, extremeImbalance: true);
            var cvdData = CreateTestCVDData(highAcceleration: true);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);
            var deltaData = CreateTestDeltaDataSeries(bar, 500m);

            var executionTimes = new List<double>();

            // Act - Run multiple iterations to test performance consistency
            for (int i = 0; i < 100; i++)
            {
                var startTime = DateTime.UtcNow;
                var result = _signalGenerator.ValidateUltraPrecisionEntry(bar + i, candle, footprintData, cvdData, volumeData, deltaData);
                var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                executionTimes.Add(executionTime);
            }

            // Assert
            var avgExecutionTime = executionTimes.Sum() / executionTimes.Count;
            var maxExecutionTime = executionTimes.Max();

            Assert.IsTrue(avgExecutionTime < 1.0, $"Average execution time should be <1ms, actual: {avgExecutionTime:F2}ms");
            Assert.IsTrue(maxExecutionTime < 2.0, $"Maximum execution time should be <2ms, actual: {maxExecutionTime:F2}ms");
        }

        [TestMethod]
        public void ValidateUltraPrecisionEntry_WithRealDataValidation_ShouldEnforceCompliance()
        {
            // Arrange
            var bar = 100;
            var oldCandle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            oldCandle.Time = DateTime.UtcNow.AddMinutes(-10); // Old data - should trigger validation failure

            var footprintData = CreateTestFootprintData(highInstitutionalFlow: true, extremeImbalance: true);
            var cvdData = CreateTestCVDData(highAcceleration: true);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);
            var deltaData = CreateTestDeltaDataSeries(bar, 500m);

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
            {
                _signalGenerator.ValidateUltraPrecisionEntry(bar, oldCandle, footprintData, cvdData, volumeData, deltaData);
            }, "Should throw exception for non-live data");
        }

        [TestMethod]
        public void GetSignalStatistics_AfterMultipleCalculations_ShouldProvideAccurateMetrics()
        {
            // Arrange
            var bar = 100;
            var candle = CreateTestCandle(2500m, 2505m, 2495m, 2502m, 1000m);
            var footprintData = CreateTestFootprintData(highInstitutionalFlow: true, extremeImbalance: true);
            var cvdData = CreateTestCVDData(highAcceleration: true);
            var volumeData = CreateTestVolumeDataSeries(bar, 1000m);
            var deltaData = CreateTestDeltaDataSeries(bar, 500m);

            // Act - Generate multiple signals
            for (int i = 0; i < 10; i++)
            {
                _signalGenerator.ValidateUltraPrecisionEntry(bar + i, candle, footprintData, cvdData, volumeData, deltaData);
            }

            var stats = _signalGenerator.GetSignalStatistics();

            // Assert
            Assert.IsNotNull(stats, "Statistics should not be null");
            Assert.AreEqual(10, stats.TotalCalculations, "Should track total calculations");
            Assert.IsTrue(stats.AverageExecutionTimeMs < 1.0, $"Average execution time should be <1ms, actual: {stats.AverageExecutionTimeMs:F2}ms");
            Assert.IsTrue(stats.SignalAccuracy >= 0, "Signal accuracy should be non-negative");
            Assert.AreEqual(0.95m, stats.ConfidenceTarget, "Confidence target should be 95%");
        }

        #region Helper Methods

        private IndicatorCandle CreateTestCandle(decimal open, decimal high, decimal low, decimal close, decimal volume)
        {
            return new IndicatorCandle
            {
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Time = DateTime.UtcNow
            };
        }

        private FootprintData CreateTestFootprintData(bool highInstitutionalFlow, bool extremeImbalance)
        {
            var data = new FootprintData
            {
                IsRealData = true,
                DataSource = "ATAS_LIVE_FEED"
            };

            if (highInstitutionalFlow)
            {
                data.InstitutionalFlowScore = 2.5m; // >80% threshold (2.0)
                data.HasInstitutionalFlow = true;
                data.InstitutionalFlowStrength = 85m;
            }

            if (extremeImbalance)
            {
                data.BidAskImbalance = new BidAskImbalanceData
                {
                    BidVolume = 900m,
                    AskVolume = 100m, // 9:1 ratio > 8:1 threshold
                    ImbalanceRatio = 0.9m,
                    IsSignificant = true
                };
                data.HasBidAskImbalance = true;
            }

            // Set absorption strength for liquidity vacuum layer
            data.AbsorptionStrength = 0.9m; // >85% threshold

            return data;
        }

        private CVDData CreateTestCVDData(bool highAcceleration)
        {
            var data = new CVDData();

            if (highAcceleration)
            {
                data.MomentumAcceleration = 2.5m; // >2x threshold
                data.HasMomentumBuilding = true;
            }

            return data;
        }

        private IDataSeries<decimal> CreateTestVolumeDataSeries(int currentBar, decimal currentVolume)
        {
            var series = new ValueDataSeries("TestVolume");
            
            // Create baseline volume history
            for (int i = Math.Max(0, currentBar - 300); i < currentBar; i++)
            {
                series[i] = currentVolume * 0.2m; // Low baseline volume
            }
            
            // Set current volume high to trigger velocity spike
            series[currentBar] = currentVolume; // 5x baseline = velocity spike

            return series;
        }

        private IDataSeries<decimal> CreateTestDeltaDataSeries(int currentBar, decimal currentDelta)
        {
            var series = new ValueDataSeries("TestDelta");
            
            for (int i = Math.Max(0, currentBar - 100); i <= currentBar; i++)
            {
                series[i] = currentDelta;
            }

            return series;
        }

        #endregion
    }

    /// <summary>
    /// Mock ChartStrategy for testing
    /// </summary>
    public class MockChartStrategy : ChartStrategy
    {
        public MockChartStrategy() : base(true) { }

        public override void LogInfo(string message) { /* Mock implementation */ }
        public override void LogWarning(string message) { /* Mock implementation */ }
        public override void LogError(string message) { /* Mock implementation */ }
        public override void LogDebug(string message) { /* Mock implementation */ }

        protected override void OnCalculate(int bar, decimal value) { /* Mock implementation */ }
    }
}
