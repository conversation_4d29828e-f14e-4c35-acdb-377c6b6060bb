using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;
using ATAS.Indicators;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 PHASE 3: Unit tests for AdaptiveParameterOptimizer
    /// 
    /// Tests cover:
    /// - Parameter optimization functionality
    /// - Machine learning adjustments
    /// - Market regime adaptation
    /// - Performance improvement tracking
    /// - Emergency mode handling
    /// - Performance targets (<2ms execution time)
    /// </summary>
    [TestClass]
    public class AdaptiveParameterOptimizerTests
    {
        private TestChartStrategy _mockStrategy;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private AdaptiveParameterOptimizer _optimizer;

        [TestInitialize]
        public void Setup()
        {
            // Create mock strategy
            _mockStrategy = new TestChartStrategy();

            // Create test configuration
            _config = new OrderFlowConfiguration
            {
                FootprintDeltaThreshold = 1000m,
                CVDDivergenceThreshold = 1.0m,
                VolumeProfileSignificanceThreshold = 2.0m,
                ConfluenceMinimumScore = 75m,
                SignalStrengthThreshold = 70m
            };

            _assetConfig = new AssetConfiguration
            {
                StopLossPercentage = 0.3m,
                TakeProfitPercentage = 0.4m,
                PositionSizePercentage = 1.0m
            };

            // Create optimizer
            _optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _optimizer?.Dispose();
        }

        [TestMethod]
        public void Constructor_ValidParameters_InitializesSuccessfully()
        {
            // Arrange & Act
            using var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);

            // Assert
            Assert.IsNotNull(optimizer);
            Assert.AreEqual(0m, optimizer.CurrentPerformanceScore);
            Assert.AreEqual(0m, optimizer.PerformanceImprovement);
            Assert.IsTrue(optimizer.IsOptimizationEnabled);
            Assert.AreEqual(MarketRegime.Unknown, optimizer.CurrentRegime);
            Assert.AreEqual(0, optimizer.OptimizationCycles);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_NullStrategy_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            new AdaptiveParameterOptimizer(null, _config, _assetConfig);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_NullConfig_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            new AdaptiveParameterOptimizer(_mockStrategy, null, _assetConfig);
        }

        [TestMethod]
        public void OptimizeParameters_FirstCall_ReturnsIntervalNotElapsed()
        {
            // Arrange
            var marketCondition = CreateTestMarketCondition();
            var performanceMetric = CreateTestPerformanceMetric();

            // Act
            var result = _optimizer.OptimizeParameters(100, marketCondition, performanceMetric);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual("Optimization interval not elapsed", result.Reason);
        }

        [TestMethod]
        public void OptimizeParameters_HighVolatilityRegime_AppliesConservativeParameters()
        {
            // Arrange
            var marketCondition = new MarketCondition
            {
                Volatility = 0.9m, // High volatility
                TrendStrength = 0.5m,
                PriceRange = 100m,
                AverageRange = 80m,
                Volume = 1000m,
                AverageVolume = 800m
            };
            var performanceMetric = CreateTestPerformanceMetric();

            // Force optimization by setting last optimization time to past
            var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);
            
            // Use reflection to set last optimization time (for testing)
            var field = typeof(AdaptiveParameterOptimizer).GetField("_lastOptimization", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(optimizer, DateTime.UtcNow.AddMinutes(-10));

            // Act
            var result = optimizer.OptimizeParameters(100, marketCondition, performanceMetric);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(MarketRegime.HighVolatility, result.TargetRegime);
            Assert.IsTrue(result.OptimizedParameters.Count > 0);

            // Cleanup
            optimizer.Dispose();
        }

        [TestMethod]
        public void OptimizeParameters_LowVolatilityRegime_AppliesAggressiveParameters()
        {
            // Arrange
            var marketCondition = new MarketCondition
            {
                Volatility = 0.2m, // Low volatility
                TrendStrength = 0.3m,
                PriceRange = 20m,
                AverageRange = 30m,
                Volume = 500m,
                AverageVolume = 600m
            };
            var performanceMetric = CreateTestPerformanceMetric();

            // Force optimization
            var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);
            var field = typeof(AdaptiveParameterOptimizer).GetField("_lastOptimization", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(optimizer, DateTime.UtcNow.AddMinutes(-10));

            // Act
            var result = optimizer.OptimizeParameters(100, marketCondition, performanceMetric);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(MarketRegime.LowVolatility, result.TargetRegime);
            Assert.IsTrue(result.OptimizedParameters.Count > 0);

            // Cleanup
            optimizer.Dispose();
        }

        [TestMethod]
        public void OptimizeParameters_PerformanceTargets_MeetsExecutionTime()
        {
            // Arrange
            var marketCondition = CreateTestMarketCondition();
            var performanceMetric = CreateTestPerformanceMetric();

            // Force optimization
            var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);
            var field = typeof(AdaptiveParameterOptimizer).GetField("_lastOptimization", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(optimizer, DateTime.UtcNow.AddMinutes(-10));

            // Act
            var result = optimizer.OptimizeParameters(100, marketCondition, performanceMetric);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.IsTrue(result.ExecutionTimeMs < 2.0m, $"Execution time {result.ExecutionTimeMs}ms exceeds 2ms target");

            // Cleanup
            optimizer.Dispose();
        }

        [TestMethod]
        public void GetCurrentParameters_AfterOptimization_ReturnsUpdatedParameters()
        {
            // Arrange
            var marketCondition = CreateTestMarketCondition();
            var performanceMetric = CreateTestPerformanceMetric();

            // Force optimization
            var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);
            var field = typeof(AdaptiveParameterOptimizer).GetField("_lastOptimization", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(optimizer, DateTime.UtcNow.AddMinutes(-10));

            // Act
            var result = optimizer.OptimizeParameters(100, marketCondition, performanceMetric);
            var parameters = optimizer.GetCurrentParameters();

            // Assert
            Assert.IsTrue(result.Success);
            Assert.IsNotNull(parameters);
            Assert.IsTrue(parameters.Count > 0);
            Assert.IsTrue(parameters.ContainsKey("FootprintDeltaThreshold"));
            Assert.IsTrue(parameters.ContainsKey("StopLossPercentage"));

            // Cleanup
            optimizer.Dispose();
        }

        [TestMethod]
        public void ForceEmergencyMode_ValidReason_ActivatesEmergencyMode()
        {
            // Arrange
            var reason = "Test emergency activation";

            // Act
            _optimizer.ForceEmergencyMode(reason);

            // Assert
            Assert.IsFalse(_optimizer.IsOptimizationEnabled);
        }

        [TestMethod]
        public void ResetToBaseline_AfterEmergencyMode_RestoresNormalOperation()
        {
            // Arrange
            _optimizer.ForceEmergencyMode("Test emergency");
            Assert.IsFalse(_optimizer.IsOptimizationEnabled);

            // Act
            _optimizer.ResetToBaseline();

            // Assert
            Assert.IsTrue(_optimizer.IsOptimizationEnabled);
        }

        [TestMethod]
        public void Dispose_MultipleCallsSafe_NoExceptions()
        {
            // Arrange
            var optimizer = new AdaptiveParameterOptimizer(_mockStrategy, _config, _assetConfig);

            // Act & Assert - Should not throw
            optimizer.Dispose();
            optimizer.Dispose(); // Second call should be safe
        }

        #region Helper Methods

        private MarketCondition CreateTestMarketCondition()
        {
            return new MarketCondition
            {
                Volatility = 0.5m,
                TrendStrength = 0.3m,
                PriceRange = 50m,
                AverageRange = 45m,
                Volume = 1000m,
                AverageVolume = 950m,
                Timestamp = DateTime.UtcNow
            };
        }

        private PerformanceMetric CreateTestPerformanceMetric()
        {
            return new PerformanceMetric
            {
                WinRate = 0.7m,
                ProfitFactor = 1.8m,
                AverageWin = 0.4m,
                AverageLoss = 0.25m,
                MaxDrawdown = 0.08m,
                SharpeRatio = 1.5m,
                TotalTrades = 50,
                Timestamp = DateTime.UtcNow
            };
        }

        #endregion
    }

    /// <summary>
    /// Mock ChartStrategy for testing
    /// </summary>
    public class TestChartStrategy : ChartStrategy
    {
        public List<string> LogMessages { get; } = new List<string>();

        public void LogInfo(string message) => LogMessages.Add($"INFO: {message}");
        public void LogWarning(string message) => LogMessages.Add($"WARNING: {message}");
        public void LogError(string message) => LogMessages.Add($"ERROR: {message}");
        public void LogDebug(string message) => LogMessages.Add($"DEBUG: {message}");
    }
}
