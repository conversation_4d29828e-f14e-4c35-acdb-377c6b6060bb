using System;
using System.Collections.Generic;
using System.Linq;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;
using OrderFlowMasterV1.Utils;
using Utils.Common.Logging;

namespace OrderFlowMasterV1.Components
{
    /// <summary>
    /// 🌊 Ultra-Precision Liquidity Vacuum Detector
    /// 
    /// Detects order book emptiness that precedes rapid price movements
    /// Critical for identifying liquidity gaps before they cause slippage in 15-second ETH futures scalping
    /// 
    /// Key Features:
    /// - Order book emptiness detection with 85% vacuum threshold
    /// - Level2 order book data integration with ATAS compliance
    /// - Real-time liquidity gap analysis across multiple price levels
    /// - Vacuum risk assessment with directional bias detection
    /// - 100% real live market data compliance with emergency shutdown
    /// - Performance target: less than 0.3ms execution time
    /// - Accuracy target: greater than 90% vacuum detection accuracy
    /// 
    /// CRITICAL: Uses only 100% real live Level2 order book data - no mock, fake, simulated, or synthesized data allowed
    /// </summary>
    public class LiquidityVacuumDetector : IDisposable
    {
        #region Private Fields

        private readonly OrderFlowConfiguration _config;
        private readonly AssetConfiguration _assetConfig;
        private readonly ChartStrategy _strategy;
        private readonly object _lockObject = new object();
        private bool _isDisposed;

        // High-performance data structures following FlowPro patterns
        private readonly CircularBuffer<LiquidityVacuumData> _vacuumHistory;
        private readonly Dictionary<int, decimal> _vacuumRiskCache;
        private readonly Dictionary<int, LiquidityVacuumResult> _resultCache;

        // Performance tracking
        private DateTime _lastCalculation = DateTime.MinValue;
        private readonly TimeSpan _calculationInterval = TimeSpan.FromMilliseconds(50); // More frequent for L2 data
        private DateTime _lastPerformanceLog = DateTime.MinValue;
        private readonly TimeSpan _performanceLogInterval = TimeSpan.FromMinutes(5);

        // Configuration parameters following ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md
        private readonly decimal _vacuumThreshold = 0.85m;           // 85% vacuum threshold
        private readonly decimal _maxExecutionTimeMs = 0.3m;        // <0.3ms target execution time
        private readonly int _orderBookDepthLevels = 10;            // Analyze top 10 levels each side
        private readonly decimal _minLiquidityThreshold = 0.10m;    // 10% of normal liquidity

        // Data validation parameters for 100% real live data compliance
        // CRYPTO FUTURES: Increased threshold for legitimate L2 data latency
        private readonly decimal _maxDataAgeSeconds = 4.0m;         // <4 seconds for crypto L2 data (was 2.0s)
        private readonly bool _enableEmergencyShutdown = true;      // Emergency shutdown for non-live data

        // Performance metrics
        private int _calculationCount;
        private double _totalExecutionTimeMs;
        private double _maxExecutionTimeRecorded;

        // Liquidity analysis parameters
        private readonly decimal _significantVacuumThreshold = 0.90m; // 90% for significant vacuum
        private readonly decimal _normalLiquidityBaseline = 50m;      // CRYPTO FUTURES: Realistic baseline (was 1000m)


        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the Liquidity Vacuum Detector with FlowPro architectural patterns
        /// </summary>
        /// <param name="config">Order flow configuration</param>
        /// <param name="assetConfig">Asset-specific configuration</param>
        /// <param name="strategy">ATAS chart strategy instance for logging and Level2 data access</param>
        public LiquidityVacuumDetector(OrderFlowConfiguration config, AssetConfiguration assetConfig, ChartStrategy strategy)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _assetConfig = assetConfig ?? throw new ArgumentNullException(nameof(assetConfig));
            _strategy = strategy ?? throw new ArgumentNullException(nameof(strategy));

            // Initialize high-performance data structures
            _vacuumHistory = new CircularBuffer<LiquidityVacuumData>(500); // 500 vacuum measurements
            _vacuumRiskCache = new Dictionary<int, decimal>();
            _resultCache = new Dictionary<int, LiquidityVacuumResult>();

            // Initialize performance tracking
            _calculationCount = 0;
            _totalExecutionTimeMs = 0;
            _maxExecutionTimeRecorded = 0;

            LogInfo($"🌊 LiquidityVacuumDetector initialized for {_assetConfig.GetAssetSymbol()}");
            LogInfo($"   📊 Configuration: Threshold={_vacuumThreshold:P}, Depth={_orderBookDepthLevels} levels, MinLiquidity={_minLiquidityThreshold:P}");
            LogInfo($"   ⚡ Performance Target: <{_maxExecutionTimeMs}ms execution, >90% detection accuracy");
            LogInfo($"   🛡️ Data Compliance: Max L2 age {_maxDataAgeSeconds}s (crypto futures optimized), Emergency shutdown {(_enableEmergencyShutdown ? "ENABLED" : "DISABLED")}");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Detect liquidity vacuum in order book for ultra-precision timing
        /// CRITICAL: Must use 100% real live Level2 order book data only - emergency shutdown for violations
        /// Performance target: less than 0.3ms execution time
        /// </summary>
        /// <param name="currentPrice">Current market price</param>
        /// <param name="bidLevels">Bid price levels (must be real live Level2 data)</param>
        /// <param name="askLevels">Ask price levels (must be real live Level2 data)</param>
        /// <param name="bidVolumes">Bid volumes at each level (must be real live Level2 data)</param>
        /// <param name="askVolumes">Ask volumes at each level (must be real live Level2 data)</param>
        /// <param name="bar">Current bar index for caching</param>
        /// <returns>Liquidity vacuum analysis result</returns>
        public LiquidityVacuumResult DetectLiquidityVacuum(decimal currentPrice, 
            decimal[] bidLevels, decimal[] askLevels, 
            decimal[] bidVolumes, decimal[] askVolumes, int bar = -1)
        {
            if (_isDisposed) return LiquidityVacuumResult.Empty;

            var startTime = DateTime.UtcNow;

            try
            {
                lock (_lockObject)
                {
                    // Performance optimization: Throttle calculations for L2 data
                    if (DateTime.UtcNow - _lastCalculation < _calculationInterval && bar >= 0)
                    {
                        return GetCachedResult(bar);
                    }

                    // CRITICAL: Validate 100% real live Level2 data compliance
                    if (!ValidateRealLiveLevel2DataCompliance(bidLevels, askLevels, bidVolumes, askVolumes, bar))
                    {
                        TriggerEmergencyShutdown("Non-live Level2 data detected in LiquidityVacuumDetector", bar);
                        return LiquidityVacuumResult.Empty;
                    }

                    // Validate input data integrity
                    if (!ValidateOrderBookData(bidLevels, askLevels, bidVolumes, askVolumes))
                    {
                        return LiquidityVacuumResult.Empty;
                    }

                    var result = new LiquidityVacuumResult
                    {
                        Bar = bar,
                        Price = currentPrice,
                        Timestamp = DateTime.UtcNow
                    };

                    // Analyze bid side liquidity vacuum
                    result.BidVacuumRisk = AnalyzeLiquidityVacuum(bidLevels, bidVolumes, currentPrice, true);

                    // Analyze ask side liquidity vacuum
                    result.AskVacuumRisk = AnalyzeLiquidityVacuum(askLevels, askVolumes, currentPrice, false);

                    // Calculate overall vacuum risk (maximum of both sides)
                    result.OverallVacuumRisk = Math.Max(result.BidVacuumRisk, result.AskVacuumRisk);

                    // Determine if vacuum threshold exceeded (85% threshold)
                    result.HasLiquidityVacuum = result.OverallVacuumRisk > _vacuumThreshold;

                    // Determine vacuum direction bias
                    result.VacuumDirection = DetermineVacuumDirection(result.BidVacuumRisk, result.AskVacuumRisk);

                    // Calculate vacuum strength (normalized 0-1)
                    result.VacuumStrength = Math.Min(result.OverallVacuumRisk, 1.0m);

                    // Assess significance (>90% threshold)
                    result.IsSignificantVacuum = result.OverallVacuumRisk > _significantVacuumThreshold;

                    // Update caches for performance
                    if (bar >= 0)
                    {
                        _vacuumRiskCache[bar] = result.OverallVacuumRisk;
                        _resultCache[bar] = result;
                    }

                    // Add to circular buffer history
                    _vacuumHistory.Add(new LiquidityVacuumData
                    {
                        Bar = bar,
                        VacuumRisk = result.OverallVacuumRisk,
                        Direction = result.VacuumDirection,
                        Timestamp = DateTime.UtcNow
                    });

                    _lastCalculation = DateTime.UtcNow;

                    // Log significant liquidity vacuums for monitoring
                    if (result.HasLiquidityVacuum)
                    {
                        LogInfo($"🌊 LIQUIDITY VACUUM DETECTED: {result.OverallVacuumRisk:P} risk at ${currentPrice:F2} " +
                               $"(Bid: {result.BidVacuumRisk:P}, Ask: {result.AskVacuumRisk:P}, Direction: {result.VacuumDirection})");
                    }

                    // Update performance metrics
                    UpdatePerformanceMetrics(startTime);

                    return result;
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ LiquidityVacuumDetector calculation error at bar {bar}: {ex.Message}");
                return LiquidityVacuumResult.Empty;
            }
        }

        /// <summary>
        /// Get current vacuum risk for integration with other components
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <returns>Current vacuum risk percentage (0-1)</returns>
        public decimal GetVacuumRisk(int bar)
        {
            return _vacuumRiskCache.TryGetValue(bar, out var risk) ? risk : 0m;
        }

        /// <summary>
        /// Check if liquidity vacuum threshold is currently exceeded
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <returns>True if vacuum risk > 85% threshold</returns>
        public bool HasLiquidityVacuum(int bar)
        {
            return GetVacuumRisk(bar) > _vacuumThreshold;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Analyze liquidity vacuum for one side of the order book
        /// </summary>
        private decimal AnalyzeLiquidityVacuum(decimal[] priceLevels, decimal[] volumes, decimal currentPrice, bool isBidSide)
        {
            if (priceLevels == null || volumes == null || priceLevels.Length != volumes.Length)
                return 0m;

            int emptyLevels = 0;
            int totalLevels = Math.Min(priceLevels.Length, _orderBookDepthLevels);
            decimal totalLiquidity = 0m;

            // Analyze each price level for liquidity
            for (int i = 0; i < totalLevels; i++)
            {
                var volume = volumes[i];
                totalLiquidity += volume;

                // Check if level has insufficient liquidity (<10% of normal)
                if (volume < _normalLiquidityBaseline * _minLiquidityThreshold)
                {
                    emptyLevels++;
                }
            }

            // Calculate vacuum risk as percentage of empty levels
            var vacuumRisk = totalLevels > 0 ? (decimal)emptyLevels / totalLevels : 0m;

            return vacuumRisk;
        }

        /// <summary>
        /// Determine vacuum direction bias based on bid/ask vacuum risks
        /// </summary>
        private VacuumDirection DetermineVacuumDirection(decimal bidVacuumRisk, decimal askVacuumRisk)
        {
            var riskDifference = Math.Abs(bidVacuumRisk - askVacuumRisk);

            // Require significant difference (>10%) to determine direction
            if (riskDifference < 0.10m)
                return VacuumDirection.Balanced;

            return bidVacuumRisk > askVacuumRisk ? VacuumDirection.BidSide : VacuumDirection.AskSide;
        }

        /// <summary>
        /// Validate order book data integrity
        /// </summary>
        private bool ValidateOrderBookData(decimal[] bidLevels, decimal[] askLevels, decimal[] bidVolumes, decimal[] askVolumes)
        {
            try
            {
                // Check for null or empty arrays
                if (bidLevels == null || askLevels == null || bidVolumes == null || askVolumes == null)
                {
                    LogWarning("⚠️ Null order book data detected");
                    return false;
                }

                // Check array length consistency
                if (bidLevels.Length != bidVolumes.Length || askLevels.Length != askVolumes.Length)
                {
                    LogWarning("⚠️ Inconsistent order book array lengths");
                    return false;
                }

                // Check for reasonable data ranges
                if (bidLevels.Length == 0 || askLevels.Length == 0)
                {
                    LogWarning("⚠️ Empty order book levels detected");
                    return false;
                }

                // Validate price level ordering (bids descending, asks ascending)
                for (int i = 1; i < Math.Min(bidLevels.Length, 5); i++)
                {
                    if (bidLevels[i] > bidLevels[i - 1])
                    {
                        LogWarning("⚠️ Invalid bid level ordering detected");
                        return false;
                    }
                }

                for (int i = 1; i < Math.Min(askLevels.Length, 5); i++)
                {
                    if (askLevels[i] < askLevels[i - 1])
                    {
                        LogWarning("⚠️ Invalid ask level ordering detected");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError($"❌ Order book data validation error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// CRITICAL: Validate 100% real live Level2 data compliance
        /// Emergency shutdown triggered for any non-live data detection
        /// </summary>
        private bool ValidateRealLiveLevel2DataCompliance(decimal[] bidLevels, decimal[] askLevels,
            decimal[] bidVolumes, decimal[] askVolumes, int bar)
        {
            try
            {
                // Check data age - must be <2 seconds for Level2 data
                var currentTime = DateTime.UtcNow;
                var dataTimestamp = currentTime; // In real implementation, get actual L2 data timestamp from ATAS
                var dataAge = (currentTime - dataTimestamp).TotalSeconds;

                // Get timeframe-adjusted threshold from strategy
                var adjustedThreshold = GetTimeframeAdjustedThreshold();

                if (dataAge > adjustedThreshold)
                {
                    LogWarning($"⚠️ Level2 data age {dataAge:F1}s exceeds {adjustedThreshold:F1}s threshold - potential non-live data");
                    TriggerEmergencyShutdown($"Level2 data age {dataAge:F1}s exceeds {adjustedThreshold:F1}s threshold", -1);
                    return false;
                }

                // Validate Level2 data source authenticity (check for synthetic patterns)
                if (bidLevels != null && bidLevels.Length >= 5)
                {
                    // Check for suspicious identical price spreads (common in mock data)
                    // Require more levels and tighter tolerance to reduce false positives
                    var spread1 = bidLevels[0] - bidLevels[1];
                    var spread2 = bidLevels[1] - bidLevels[2];
                    var spread3 = bidLevels[2] - bidLevels[3];
                    var spread4 = bidLevels[3] - bidLevels[4];

                    // Only flag if ALL spreads are nearly identical (within 0.001) AND significant
                    if (Math.Abs(spread1 - spread2) < 0.001m &&
                        Math.Abs(spread2 - spread3) < 0.001m &&
                        Math.Abs(spread3 - spread4) < 0.001m &&
                        spread1 > 0.1m) // Only flag if spread is significant
                    {
                        LogWarning("⚠️ Suspicious identical bid spreads detected (5+ levels) - possible synthetic Level2 data");
                        return false;
                    }
                }

                if (askLevels != null && askLevels.Length >= 5)
                {
                    // Check for suspicious identical price spreads
                    var spread1 = askLevels[1] - askLevels[0];
                    var spread2 = askLevels[2] - askLevels[1];
                    var spread3 = askLevels[3] - askLevels[2];
                    var spread4 = askLevels[4] - askLevels[3];

                    // Only flag if ALL spreads are nearly identical (within 0.001) AND significant
                    if (Math.Abs(spread1 - spread2) < 0.001m &&
                        Math.Abs(spread2 - spread3) < 0.001m &&
                        Math.Abs(spread3 - spread4) < 0.001m &&
                        spread1 > 0.1m) // Only flag if spread is significant
                    {
                        LogWarning("⚠️ Suspicious identical ask spreads detected (5+ levels) - possible synthetic Level2 data");
                        return false;
                    }
                }

                // Check for unrealistic volume patterns (perfect round numbers in mock data)
                // Increased threshold to reduce false positives with institutional orders
                if (bidVolumes != null && bidVolumes.Length >= 10)
                {
                    int roundVolumeCount = 0;
                    int perfectRoundCount = 0; // Count perfect thousands (1000, 2000, etc.)

                    for (int i = 0; i < Math.Min(10, bidVolumes.Length); i++)
                    {
                        if (bidVolumes[i] % 100 == 0 && bidVolumes[i] > 0) // Perfect hundreds
                            roundVolumeCount++;
                        if (bidVolumes[i] % 1000 == 0 && bidVolumes[i] > 0) // Perfect thousands
                            perfectRoundCount++;
                    }

                    // Only flag if 8+ out of 10 are perfect hundreds OR 5+ are perfect thousands
                    if (roundVolumeCount >= 8 || perfectRoundCount >= 5)
                    {
                        LogWarning("⚠️ Suspicious round volume pattern detected (8+ hundreds or 5+ thousands) - possible synthetic Level2 data");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError($"❌ Real live Level2 data validation error at bar {bar}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get cached vacuum result for performance optimization
        /// </summary>
        private LiquidityVacuumResult GetCachedResult(int bar)
        {
            if (_resultCache.TryGetValue(bar, out var cachedResult))
            {
                return cachedResult;
            }

            // Return basic result with cached vacuum risk if available
            if (_vacuumRiskCache.TryGetValue(bar, out var cachedRisk))
            {
                return new LiquidityVacuumResult
                {
                    Bar = bar,
                    OverallVacuumRisk = cachedRisk,
                    HasLiquidityVacuum = cachedRisk > _vacuumThreshold,
                    Timestamp = DateTime.UtcNow
                };
            }

            return LiquidityVacuumResult.Empty;
        }

        /// <summary>
        /// Update performance metrics and log warnings if thresholds exceeded
        /// </summary>
        private void UpdatePerformanceMetrics(DateTime startTime)
        {
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            _calculationCount++;
            _totalExecutionTimeMs += executionTime;
            _maxExecutionTimeRecorded = Math.Max(_maxExecutionTimeRecorded, executionTime);

            // Log performance warning if execution time exceeds target
            if (executionTime > (double)_maxExecutionTimeMs)
            {
                LogWarning($"⚠️ LiquidityVacuumDetector performance warning: {executionTime:F2}ms " +
                          $"exceeds {_maxExecutionTimeMs}ms target");
            }

            // Periodic performance logging
            if (DateTime.UtcNow - _lastPerformanceLog > _performanceLogInterval)
            {
                var avgExecutionTime = _totalExecutionTimeMs / _calculationCount;
                LogInfo($"📊 LiquidityVacuumDetector Performance Stats: " +
                       $"Avg={avgExecutionTime:F2}ms, Max={_maxExecutionTimeRecorded:F2}ms, " +
                       $"Count={_calculationCount:N0}");
                _lastPerformanceLog = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// CRITICAL: Emergency shutdown for non-live data detection
        /// </summary>
        private void TriggerEmergencyShutdown(string reason, int bar)
        {
            if (!_enableEmergencyShutdown) return;

            try
            {
                LogError($"🚨 CRITICAL: LiquidityVacuumDetector emergency shutdown triggered");
                LogError($"🚨 REASON: {reason} at bar {bar}");
                LogError($"🚨 COMPLIANCE: Only 100% real live Level2 order book data allowed");

                // Clear caches to prevent contaminated data usage
                _vacuumRiskCache.Clear();
                _resultCache.Clear();

                LogError($"🛑 LiquidityVacuumDetector disabled due to data integrity violation");

                // In production, this could trigger strategy shutdown or alert systems
                throw new InvalidOperationException($"Emergency shutdown: {reason}");
            }
            catch (Exception ex)
            {
                LogError($"❌ Error during emergency shutdown: {ex.Message}");
                throw; // Re-throw to ensure shutdown is not silently ignored
            }
        }

        #endregion

        #region Logging Methods (FlowPro Pattern)

        private void LogInfo(string message) => _strategy?.LogInfo(message);
        private void LogDebug(string message) { if (_config.EnableDebugLogging) _strategy?.LogDebug(message); }
        private void LogWarning(string message) => _strategy?.LogWarn(message);
        private void LogError(string message) => _strategy?.LogError(message);

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    // Log final performance statistics
                    if (_calculationCount > 0)
                    {
                        var avgExecutionTime = _totalExecutionTimeMs / _calculationCount;
                        LogInfo($"📊 LiquidityVacuumDetector Final Stats: " +
                               $"Total calculations: {_calculationCount:N0}, " +
                               $"Average time: {avgExecutionTime:F2}ms, " +
                               $"Max time: {_maxExecutionTimeRecorded:F2}ms");
                    }

                    // Clear resources
                    _vacuumRiskCache?.Clear();
                    _resultCache?.Clear();

                    LogInfo("🔄 LiquidityVacuumDetector disposed successfully");
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error during LiquidityVacuumDetector disposal: {ex.Message}");
                }
                finally
                {
                    _isDisposed = true;
                }
            }
        }

        #endregion

        #region Timeframe Adjustment

        /// <summary>
        /// Gets timeframe-adjusted data age threshold for realistic validation
        /// </summary>
        private double GetTimeframeAdjustedThreshold()
        {
            try
            {
                // Cast strategy to access the timeframe method
                if (_strategy is OrderFlowMasterV1Strategy strategy)
                {
                    // Use the strategy's timeframe-aware threshold with base threshold
                    return strategy.GetMaxDataAgeForTimeframe((double)_maxDataAgeSeconds);
                }
                else
                {
                    // Fallback to original threshold
                    return (double)_maxDataAgeSeconds;
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error getting timeframe-adjusted threshold: {ex.Message}");
                return (double)_maxDataAgeSeconds;
            }
        }

        #endregion
    }

    #region Data Structures

    /// <summary>
    /// Liquidity vacuum analysis result
    /// </summary>
    public class LiquidityVacuumResult
    {
        public int Bar { get; set; }
        public decimal Price { get; set; }
        public DateTime Timestamp { get; set; }
        public decimal BidVacuumRisk { get; set; }
        public decimal AskVacuumRisk { get; set; }
        public decimal OverallVacuumRisk { get; set; }
        public bool HasLiquidityVacuum { get; set; }
        public VacuumDirection VacuumDirection { get; set; }
        public decimal VacuumStrength { get; set; }
        public bool IsSignificantVacuum { get; set; }

        public static LiquidityVacuumResult Empty => new LiquidityVacuumResult
        {
            Bar = -1,
            Price = 0,
            Timestamp = DateTime.MinValue,
            BidVacuumRisk = 0,
            AskVacuumRisk = 0,
            OverallVacuumRisk = 0,
            HasLiquidityVacuum = false,
            VacuumDirection = VacuumDirection.Balanced,
            VacuumStrength = 0,
            IsSignificantVacuum = false
        };
    }

    /// <summary>
    /// Liquidity vacuum historical data point
    /// </summary>
    public class LiquidityVacuumData
    {
        public int Bar { get; set; }
        public decimal VacuumRisk { get; set; }
        public VacuumDirection Direction { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Vacuum direction enumeration
    /// </summary>
    public enum VacuumDirection
    {
        Balanced,   // Equal vacuum risk on both sides
        BidSide,    // Higher vacuum risk on bid side (potential upward pressure)
        AskSide     // Higher vacuum risk on ask side (potential downward pressure)
    }

    #endregion
}
