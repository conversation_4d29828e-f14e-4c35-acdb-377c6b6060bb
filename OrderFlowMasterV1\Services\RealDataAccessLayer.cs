using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using ATAS.DataFeedsCore;
using Utils.Common.Logging;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Services
{
    /// <summary>
    /// Real Data Access Layer for 100% live market data compliance
    /// Provides access to real ATAS tick data, order book data, and multi-timeframe data
    /// Replaces all simulated, synthetic, and reconstructed data sources
    /// </summary>
    public class RealDataAccessLayer : IDisposable
    {
        #region Private Fields

        private readonly ChartStrategy _strategy;
        private readonly OrderFlowConfiguration _config;
        private readonly object _dataLock = new object();

        // Real tick data storage - Event-driven collection
        private readonly Dictionary<int, List<RealTickData>> _tickDataCache = new Dictionary<int, List<RealTickData>>();
        private readonly Dictionary<int, RealOrderBookData> _orderBookCache = new Dictionary<int, RealOrderBookData>();

        // Real-time data tracking
        private readonly Dictionary<int, List<MarketDataArg>> _realTimeTicksPerBar = new Dictionary<int, List<MarketDataArg>>();
        private readonly Dictionary<int, MarketDataArg> _latestBidAskPerBar = new Dictionary<int, MarketDataArg>();
        private readonly Dictionary<int, List<MarketDataArg>> _marketDepthPerBar = new Dictionary<int, List<MarketDataArg>>();


        
        // Data validation and monitoring
        private DateTime _lastTickTime = DateTime.MinValue;
        private DateTime _lastOrderBookUpdate = DateTime.MinValue;
        private int _tickDataQualityScore = 100;
        private int _orderBookQualityScore = 100;
        
        // Australian environment optimization
        private readonly Timer _latencyMonitor;
        private readonly List<TimeSpan> _latencyMeasurements = new List<TimeSpan>();
        private TimeSpan _averageLatency = TimeSpan.Zero;
        
        // Data feed redundancy
        private bool _primaryDataFeedActive = true;
        private int _dataFeedFailureCount = 0;
        private DateTime _lastDataFeedCheck = DateTime.UtcNow;

        #endregion

        #region Constructor

        public RealDataAccessLayer(ChartStrategy strategy, OrderFlowConfiguration config)
        {
            _strategy = strategy ?? throw new ArgumentNullException(nameof(strategy));
            _config = config ?? throw new ArgumentNullException(nameof(config));
            
            // Initialize latency monitoring for Australian environment
            _latencyMonitor = new Timer(MonitorLatency, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
            
            LogInfo("🔴 REAL DATA ACCESS LAYER INITIALIZED - 100% Live Market Data Only");
            LogInfo("🌏 Australian Trading Environment Optimization: ENABLED");
            LogInfo("📊 Real Tick Data Access: ENABLED");
            LogInfo("📈 Real Order Book Access: ENABLED");
            LogInfo("⏱️ Latency Monitoring: ENABLED");
        }

        #endregion

        #region Real Tick Data Access

        /// <summary>
        /// Get real tick data for buy/sell volume calculation
        /// Replaces synthetic volume calculations with real ATAS tick data
        /// </summary>
        public RealTickData GetRealTickData(int bar)
        {
            lock (_dataLock)
            {
                try
                {
                    // Validate data freshness for live trading compliance
                    if (!ValidateDataFreshness())
                    {
                        LogError("❌ CRITICAL: Data freshness validation failed - Rejecting tick data request");
                        return RealTickData.Empty;
                    }

                    if (_tickDataCache.ContainsKey(bar) && _tickDataCache[bar].Count > 0)
                    {
                        var tickData = _tickDataCache[bar].Last(); // Get most recent tick for the bar
                        
                        // Validate tick data quality
                        if (ValidateTickData(tickData))
                        {
                            LogDebug($"✅ Real tick data retrieved for bar {bar}: Buy={tickData.BuyVolume:F2}, Sell={tickData.SellVolume:F2}");
                            return tickData;
                        }
                    }

                    // If no cached data, request real-time tick data from ATAS
                    return RequestRealTimeTickData(bar);
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error accessing real tick data for bar {bar}: {ex.Message}");
                    return RealTickData.Empty;
                }
            }
        }

        /// <summary>
        /// Request real-time tick data from event-driven cache
        /// Uses data collected from ATAS OnNewTrade events for authentic market data
        /// </summary>
        private RealTickData RequestRealTimeTickData(int bar)
        {
            try
            {
                // First, check if we have real-time tick data from events
                if (_realTimeTicksPerBar.ContainsKey(bar) && _realTimeTicksPerBar[bar].Count > 0)
                {
                    // Aggregate all real-time ticks for this bar
                    var ticks = _realTimeTicksPerBar[bar];
                    var aggregatedTickData = AggregateRealTimeTicks(ticks, bar);

                    if (ValidateTickData(aggregatedTickData))
                    {
                        LogDebug($"✅ Real-time tick data aggregated for bar {bar}: Buy={aggregatedTickData.BuyVolume:F2}, Sell={aggregatedTickData.SellVolume:F2}");
                        return aggregatedTickData;
                    }
                }

                // If no real-time data available, this indicates a problem with the data feed
                LogError($"❌ CRITICAL: No real-time tick data available for bar {bar}");
                LogError($"❌ This indicates the ATAS OnNewTrade event is not firing or data feed is disconnected");

                // Return empty data to trigger emergency handling
                return RealTickData.Empty;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error in RequestRealTimeTickData: {ex.Message}");
                return RealTickData.Empty;
            }
        }

        /// <summary>
        /// Aggregate multiple real-time ticks into a single tick data object
        /// </summary>
        private RealTickData AggregateRealTimeTicks(List<MarketDataArg> ticks, int bar)
        {
            try
            {
                var aggregated = new RealTickData
                {
                    Bar = bar,
                    Timestamp = ticks.Last().Time, // Use latest timestamp
                    Price = ticks.Last().Price, // Use latest price
                    IsRealData = true,
                    DataSource = "ATAS_REAL_TIME_AGGREGATED",
                    TradeCount = ticks.Count,
                    LatencyMs = (int)(DateTime.UtcNow - ticks.Last().Time).TotalMilliseconds
                };

                // Aggregate volumes by trade direction
                decimal totalBuyVolume = 0;
                decimal totalSellVolume = 0;
                decimal totalVolume = 0;

                foreach (var tick in ticks)
                {
                    totalVolume += tick.Volume;

                    switch (tick.Direction)
                    {
                        case ATAS.Indicators.TradeDirection.Buy:
                            totalBuyVolume += tick.Volume;
                            break;
                        case ATAS.Indicators.TradeDirection.Sell:
                            totalSellVolume += tick.Volume;
                            break;
                        default: // Between or unknown
                            // Split neutral trades evenly
                            totalBuyVolume += tick.Volume / 2;
                            totalSellVolume += tick.Volume / 2;
                            break;
                    }
                }

                aggregated.BuyVolume = totalBuyVolume;
                aggregated.SellVolume = totalSellVolume;
                aggregated.TotalVolume = totalVolume;
                aggregated.Volume = totalVolume;

                return aggregated;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error aggregating real-time ticks: {ex.Message}");
                return RealTickData.Empty;
            }
        }

        #endregion

        #region Real-Time Event-Driven Data Collection (ATAS Compliant)

        /// <summary>
        /// Process real-time tick data from ATAS OnNewTrade event
        /// This is the CORRECT way to capture real tick data in ATAS
        /// </summary>
        public void ProcessRealTimeTick(MarketDataArg trade, int currentBar)
        {
            if (trade == null) return;

            lock (_dataLock)
            {
                try
                {
                    // Store raw tick data for the current bar
                    if (!_realTimeTicksPerBar.ContainsKey(currentBar))
                    {
                        _realTimeTicksPerBar[currentBar] = new List<MarketDataArg>();
                    }
                    _realTimeTicksPerBar[currentBar].Add(trade);

                    // Update tick data cache with aggregated data
                    UpdateTickDataFromRealTrade(trade, currentBar);

                    // Update data freshness tracking
                    _lastTickTime = DateTime.UtcNow;
                    _tickDataQualityScore = Math.Min(100, _tickDataQualityScore + 5); // Improve quality score

                    LogDebug($"✅ Real-time tick processed: Bar={currentBar}, Price={trade.Price}, Volume={trade.Volume}, Direction={trade.Direction}");
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error processing real-time tick: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Process real-time bid/ask changes from ATAS OnBestBidAskChanged event
        /// </summary>
        public void ProcessRealTimeDepth(MarketDataArg depth, int currentBar)
        {
            if (depth == null) return;

            lock (_dataLock)
            {
                try
                {
                    // Store latest bid/ask for the current bar
                    _latestBidAskPerBar[currentBar] = depth;

                    // Update order book cache
                    UpdateOrderBookFromRealDepth(depth, currentBar);

                    // Update data freshness tracking
                    _lastOrderBookUpdate = DateTime.UtcNow;
                    _orderBookQualityScore = Math.Min(100, _orderBookQualityScore + 5);

                    LogDebug($"✅ Real-time depth processed: Bar={currentBar}, Price={depth.Price}, Type={depth.DataType}");
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error processing real-time depth: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Process real-time market depth changes from ATAS MarketDepthsChanged event
        /// </summary>
        public void ProcessRealTimeMarketDepths(IEnumerable<MarketDataArg> depths, int currentBar)
        {
            if (depths == null) return;

            lock (_dataLock)
            {
                try
                {
                    // Store market depth data for the current bar
                    if (!_marketDepthPerBar.ContainsKey(currentBar))
                    {
                        _marketDepthPerBar[currentBar] = new List<MarketDataArg>();
                    }
                    _marketDepthPerBar[currentBar].AddRange(depths);

                    // Update order book cache with full depth data
                    foreach (var depth in depths)
                    {
                        UpdateOrderBookFromRealDepth(depth, currentBar);
                    }

                    LogDebug($"✅ Real-time market depths processed: Bar={currentBar}, Count={depths.Count()}");
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error processing real-time market depths: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Update tick data cache from real ATAS trade data
        /// </summary>
        private void UpdateTickDataFromRealTrade(MarketDataArg trade, int bar)
        {
            try
            {
                if (!_tickDataCache.ContainsKey(bar))
                {
                    _tickDataCache[bar] = new List<RealTickData>();
                }

                // Create real tick data from ATAS MarketDataArg
                var tickData = new RealTickData
                {
                    Bar = bar,
                    Timestamp = trade.Time,
                    Price = trade.Price,
                    Volume = trade.Volume,
                    IsRealData = true,
                    DataSource = "ATAS_REAL_TIME_TICK",
                    LatencyMs = (int)(DateTime.UtcNow - trade.Time).TotalMilliseconds,
                    TradeCount = 1
                };

                // Determine buy/sell volume based on trade direction
                switch (trade.Direction)
                {
                    case ATAS.Indicators.TradeDirection.Buy:
                        tickData.BuyVolume = trade.Volume;
                        tickData.SellVolume = 0;
                        break;
                    case ATAS.Indicators.TradeDirection.Sell:
                        tickData.BuyVolume = 0;
                        tickData.SellVolume = trade.Volume;
                        break;
                    default: // Between or unknown
                        // For neutral trades, split volume evenly
                        tickData.BuyVolume = trade.Volume / 2;
                        tickData.SellVolume = trade.Volume / 2;
                        break;
                }

                tickData.TotalVolume = tickData.BuyVolume + tickData.SellVolume;

                _tickDataCache[bar].Add(tickData);
            }
            catch (Exception ex)
            {
                LogError($"❌ Error updating tick data from real trade: {ex.Message}");
            }
        }

        /// <summary>
        /// Update order book cache from real ATAS depth data
        /// </summary>
        private void UpdateOrderBookFromRealDepth(MarketDataArg depth, int bar)
        {
            try
            {
                if (!_orderBookCache.ContainsKey(bar))
                {
                    _orderBookCache[bar] = new RealOrderBookData
                    {
                        Bar = bar,
                        Timestamp = depth.Time,
                        IsRealData = true,
                        DataSource = "ATAS_REAL_TIME_DEPTH",
                        LatencyMs = (int)(DateTime.UtcNow - depth.Time).TotalMilliseconds
                    };
                }

                var orderBook = _orderBookCache[bar];

                // Update bid/ask based on data type
                if (depth.IsBid)
                {
                    orderBook.BestBid = depth.Price;
                    orderBook.BestBidSize = depth.Volume;
                }
                else if (depth.IsAsk)
                {
                    orderBook.BestAsk = depth.Price;
                    orderBook.BestAskSize = depth.Volume;
                }

                // Update spread and depth calculations
                if (orderBook.BestBid > 0 && orderBook.BestAsk > 0)
                {
                    orderBook.Spread = orderBook.BestAsk - orderBook.BestBid;
                    orderBook.TotalDepth = orderBook.BestBidSize + orderBook.BestAskSize;
                }

                // Update timestamp to latest
                orderBook.Timestamp = depth.Time;
                orderBook.LatencyMs = (int)(DateTime.UtcNow - depth.Time).TotalMilliseconds;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error updating order book from real depth: {ex.Message}");
            }
        }

        #endregion

        #region Real Order Book Access

        /// <summary>
        /// Get real Level 2 order book data
        /// Replaces simulated order book with real ATAS market depth data
        /// </summary>
        public RealOrderBookData GetRealOrderBookData(int bar)
        {
            lock (_dataLock)
            {
                try
                {
                    // Validate data freshness
                    if (!ValidateDataFreshness())
                    {
                        LogError("❌ CRITICAL: Data freshness validation failed - Rejecting order book request");
                        return RealOrderBookData.Empty;
                    }

                    if (_orderBookCache.ContainsKey(bar))
                    {
                        var orderBookData = _orderBookCache[bar];
                        if (ValidateOrderBookData(orderBookData))
                        {
                            LogDebug($"✅ Real order book data retrieved for bar {bar}: Depth={orderBookData.TotalDepth:F2}");
                            return orderBookData;
                        }
                    }

                    // Request real-time order book data from ATAS
                    return RequestRealTimeOrderBookData(bar);
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error accessing real order book data for bar {bar}: {ex.Message}");
                    return RealOrderBookData.Empty;
                }
            }
        }

        /// <summary>
        /// Request real-time order book data from event-driven cache
        /// Uses data collected from ATAS OnBestBidAskChanged and MarketDepthsChanged events
        /// </summary>
        private RealOrderBookData RequestRealTimeOrderBookData(int bar)
        {
            try
            {
                // First, check if we have real-time order book data from events
                if (_latestBidAskPerBar.ContainsKey(bar) || _marketDepthPerBar.ContainsKey(bar))
                {
                    var orderBookData = new RealOrderBookData
                    {
                        Bar = bar,
                        Timestamp = DateTime.UtcNow,
                        IsRealData = true,
                        DataSource = "ATAS_REAL_TIME_ORDER_BOOK",
                        LatencyMs = 0
                    };

                    // Use latest bid/ask data if available
                    if (_latestBidAskPerBar.ContainsKey(bar))
                    {
                        var latestDepth = _latestBidAskPerBar[bar];
                        orderBookData.Timestamp = latestDepth.Time;
                        orderBookData.LatencyMs = (int)(DateTime.UtcNow - latestDepth.Time).TotalMilliseconds;

                        if (latestDepth.IsBid)
                        {
                            orderBookData.BestBid = latestDepth.Price;
                            orderBookData.BestBidSize = latestDepth.Volume;
                        }
                        else if (latestDepth.IsAsk)
                        {
                            orderBookData.BestAsk = latestDepth.Price;
                            orderBookData.BestAskSize = latestDepth.Volume;
                        }
                    }

                    decimal totalBidVolume = 0m;
                    decimal totalAskVolume = 0m;
                    var largeOrders = new List<RealLargeOrder>();

                    // Process market depth data if available
                    if (_marketDepthPerBar.ContainsKey(bar))
                    {
                        var depthData = _marketDepthPerBar[bar];
                        foreach (var depth in depthData)
                        {
                            if (depth.IsBid)
                            {
                                totalBidVolume += depth.Volume;
                                orderBookData.BestBid = Math.Max(orderBookData.BestBid, depth.Price);
                                orderBookData.BestBidSize += depth.Volume;

                                // Detect large bid orders
                                if (depth.Volume >= _config.LargeOrderThreshold)
                                {
                                    largeOrders.Add(new RealLargeOrder
                                    {
                                        Price = depth.Price,
                                        Volume = depth.Volume,
                                        Side = Models.OrderSide.Bid,
                                        Timestamp = depth.Time,
                                        IsInstitutional = depth.Volume >= _config.LargeOrderThreshold * 2m
                                    });
                                }
                            }
                            else if (depth.IsAsk)
                            {
                                totalAskVolume += depth.Volume;
                                orderBookData.BestAsk = orderBookData.BestAsk == 0 ? depth.Price : Math.Min(orderBookData.BestAsk, depth.Price);
                                orderBookData.BestAskSize += depth.Volume;

                                // Detect large ask orders
                                if (depth.Volume >= _config.LargeOrderThreshold)
                                {
                                    largeOrders.Add(new RealLargeOrder
                                    {
                                        Price = depth.Price,
                                        Volume = depth.Volume,
                                        Side = Models.OrderSide.Ask,
                                        Timestamp = depth.Time,
                                        IsInstitutional = depth.Volume >= _config.LargeOrderThreshold * 2m
                                    });
                                }
                            }
                        }
                    }

                    // Calculate derived metrics
                    orderBookData.BidDepth = totalBidVolume;
                    orderBookData.AskDepth = totalAskVolume;
                    orderBookData.TotalDepth = totalBidVolume + totalAskVolume;
                    orderBookData.LargeOrders = largeOrders;
                    orderBookData.HasInstitutionalActivity = largeOrders.Any(o => o.IsInstitutional);

                    if (orderBookData.BestBid > 0 && orderBookData.BestAsk > 0)
                    {
                        orderBookData.Spread = orderBookData.BestAsk - orderBookData.BestBid;
                    }

                    orderBookData.OrderBookImbalance = totalBidVolume + totalAskVolume > 0 ?
                        (totalBidVolume - totalAskVolume) / (totalBidVolume + totalAskVolume) : 0m;

                    // Cache the real data
                    _orderBookCache[bar] = orderBookData;

                    LogDebug($"✅ Real-time order book data processed for bar {bar}: Bid={totalBidVolume:F2}, Ask={totalAskVolume:F2}, Large Orders={largeOrders.Count}");
                    return orderBookData;
                }

                // If no real-time data available, this indicates a problem with the data feed
                LogError($"❌ CRITICAL: No real-time order book data available for bar {bar}");
                LogError($"❌ This indicates the ATAS market depth events are not firing or data feed is disconnected");

                // Return empty data to trigger emergency handling
                return RealOrderBookData.Empty;
            }
            catch (Exception ex)
            {
                LogError($"❌ Failed to request real-time order book data: {ex.Message}");
                return RealOrderBookData.Empty;
            }
        }

        #endregion

        #region Data Validation

        /// <summary>
        /// Validate data freshness for live trading compliance
        /// Ensures all data is real-time and not historical/delayed
        /// ENHANCED: Stricter validation for crypto futures (5-second maximum)
        /// </summary>
        private bool ValidateDataFreshness()
        {
            var now = DateTime.UtcNow;

            // TIMEFRAME-AWARE: Check tick data freshness using dynamic thresholds
            var tickDataThreshold = GetTimeframeAdjustedThreshold(5.0); // Base 5s for tick data
            if (_lastTickTime != DateTime.MinValue && (now - _lastTickTime).TotalSeconds > tickDataThreshold)
            {
                LogError($"❌ CRITICAL: Tick data is {(now - _lastTickTime).TotalSeconds:F1} seconds old - exceeds {tickDataThreshold:F1}s threshold");
                _tickDataQualityScore = Math.Max(0, _tickDataQualityScore - 25); // Harsher penalty

                // EMERGENCY: Trigger strategy shutdown if data is too stale (2x threshold)
                var emergencyThreshold = tickDataThreshold * 2;
                if ((now - _lastTickTime).TotalSeconds > emergencyThreshold)
                {
                    LogError($"❌ EMERGENCY SHUTDOWN: Tick data age {(now - _lastTickTime).TotalSeconds:F1}s exceeds emergency threshold {emergencyThreshold:F1}s");
                    return false; // This will trigger emergency shutdown
                }
            }

            // TIMEFRAME-AWARE: Check order book freshness using dynamic thresholds
            var orderBookThreshold = GetTimeframeAdjustedThreshold(3.0); // Base 3s for order book
            if (_lastOrderBookUpdate != DateTime.MinValue && (now - _lastOrderBookUpdate).TotalSeconds > orderBookThreshold)
            {
                LogError($"❌ CRITICAL: Order book data is {(now - _lastOrderBookUpdate).TotalSeconds:F1} seconds old - exceeds {orderBookThreshold:F1}s threshold");
                _orderBookQualityScore = Math.Max(0, _orderBookQualityScore - 30); // Harsher penalty

                // EMERGENCY: Trigger strategy shutdown if order book data is too stale (2.5x threshold)
                var emergencyThreshold = orderBookThreshold * 2.5;
                if ((now - _lastOrderBookUpdate).TotalSeconds > emergencyThreshold)
                {
                    LogError($"❌ EMERGENCY SHUTDOWN: Order book age {(now - _lastOrderBookUpdate).TotalSeconds:F1}s exceeds emergency threshold {emergencyThreshold:F1}s");
                    return false; // This will trigger emergency shutdown
                }
            }
            
            // Fail validation if data quality is too low
            if (_tickDataQualityScore < 50 || _orderBookQualityScore < 50)
            {
                LogError("❌ CRITICAL: Data quality below acceptable threshold - Live trading safety violation");
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// Validate tick data authenticity and quality
        /// ENHANCED: Stricter validation for crypto futures live trading
        /// </summary>
        private bool ValidateTickData(RealTickData tickData)
        {
            if (tickData == null || !tickData.IsRealData)
            {
                LogError("❌ Tick data validation failed: null or non-real data");
                return false;
            }

            // CRITICAL: Validate data source is ATAS-compliant
            if (!tickData.DataSource.StartsWith("ATAS_"))
            {
                LogError($"❌ Tick data validation failed: invalid data source '{tickData.DataSource}'");
                return false;
            }

            // Check for impossible values
            if (tickData.BuyVolume < 0 || tickData.SellVolume < 0)
            {
                LogError($"❌ Tick data validation failed: negative volumes - Buy:{tickData.BuyVolume}, Sell:{tickData.SellVolume}");
                return false;
            }

            // TIMEFRAME-AWARE: Check data age using dynamic thresholds
            var dataAge = (DateTime.UtcNow - tickData.Timestamp).TotalSeconds;
            var ageThreshold = GetTimeframeAdjustedThreshold(5.0); // Base 5s for tick data
            if (dataAge > ageThreshold)
            {
                LogError($"❌ Tick data validation failed: data age {dataAge:F1}s exceeds {ageThreshold:F1}s threshold");
                return false;
            }

            // Validate price is reasonable (not zero or negative)
            if (tickData.Price <= 0)
            {
                LogError($"❌ Tick data validation failed: invalid price {tickData.Price}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Validate order book data authenticity and quality
        /// ENHANCED: Stricter validation for crypto futures live trading
        /// </summary>
        private bool ValidateOrderBookData(RealOrderBookData orderBookData)
        {
            if (orderBookData == null || !orderBookData.IsRealData)
            {
                LogError("❌ Order book validation failed: null or non-real data");
                return false;
            }

            // CRITICAL: Validate data source is ATAS-compliant
            if (!orderBookData.DataSource.StartsWith("ATAS_"))
            {
                LogError($"❌ Order book validation failed: invalid data source '{orderBookData.DataSource}'");
                return false;
            }

            // Check for impossible values
            if (orderBookData.BidDepth < 0 || orderBookData.AskDepth < 0)
            {
                LogError($"❌ Order book validation failed: negative depths - Bid:{orderBookData.BidDepth}, Ask:{orderBookData.AskDepth}");
                return false;
            }

            // TIMEFRAME-AWARE: Check data age using dynamic thresholds
            var dataAge = (DateTime.UtcNow - orderBookData.Timestamp).TotalSeconds;
            var ageThreshold = GetTimeframeAdjustedThreshold(3.0); // Base 3s for order book
            if (dataAge > ageThreshold)
            {
                LogError($"❌ Order book validation failed: data age {dataAge:F1}s exceeds {ageThreshold:F1}s threshold");
                return false;
            }

            // Validate total depth is reasonable
            if (orderBookData.TotalDepth <= 0)
            {
                LogError($"❌ Order book validation failed: invalid total depth {orderBookData.TotalDepth}");
                return false;
            }

            return true;
        }

        #endregion

        #region Australian Environment Optimization

        /// <summary>
        /// Monitor latency for Australian trading environment
        /// Implements latency compensation and connection quality monitoring
        /// </summary>
        private void MonitorLatency(object state)
        {
            try
            {
                var startTime = DateTime.UtcNow;
                
                // Ping the data feed to measure latency
                var bestBid = _strategy.BestBid;
                var bestAsk = _strategy.BestAsk;
                
                var latency = DateTime.UtcNow - startTime;
                
                lock (_dataLock)
                {
                    _latencyMeasurements.Add(latency);
                    
                    // Keep only last 60 measurements (1 minute)
                    if (_latencyMeasurements.Count > 60)
                        _latencyMeasurements.RemoveAt(0);
                    
                    // Calculate average latency
                    _averageLatency = TimeSpan.FromMilliseconds(_latencyMeasurements.Average(l => l.TotalMilliseconds));
                    
                    // Check for high latency (>100ms concerning for Australian environment)
                    if (_averageLatency.TotalMilliseconds > 100)
                    {
                        LogWarn($"🌏 HIGH LATENCY WARNING: {_averageLatency.TotalMilliseconds:F1}ms (Australian environment)");
                    }
                    
                    // Monitor data feed health
                    MonitorDataFeedHealth();
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Latency monitoring error: {ex.Message}");
            }
        }

        /// <summary>
        /// Monitor data feed health and implement redundancy
        /// </summary>
        private void MonitorDataFeedHealth()
        {
            var now = DateTime.UtcNow;
            
            // Check if we've received recent data
            bool recentTickData = (now - _lastTickTime).TotalSeconds < 60;
            bool recentOrderBookData = (now - _lastOrderBookUpdate).TotalSeconds < 30;
            
            if (!recentTickData || !recentOrderBookData)
            {
                _dataFeedFailureCount++;
                LogWarn($"⚠️ Data feed health warning: Tick={recentTickData}, OrderBook={recentOrderBookData}, Failures={_dataFeedFailureCount}");
                
                // If too many failures, consider data feed compromised
                if (_dataFeedFailureCount > 5)
                {
                    _primaryDataFeedActive = false;
                    LogError("❌ CRITICAL: Primary data feed appears compromised - Live trading safety risk");
                }
            }
            else
            {
                // Reset failure count on successful data
                if (_dataFeedFailureCount > 0)
                {
                    _dataFeedFailureCount = Math.Max(0, _dataFeedFailureCount - 1);
                    if (!_primaryDataFeedActive && _dataFeedFailureCount == 0)
                    {
                        _primaryDataFeedActive = true;
                        LogInfo("✅ Primary data feed restored");
                    }
                }
            }
            
            _lastDataFeedCheck = now;
        }

        #endregion

        #region Logging

        private void LogInfo(string message) => _strategy.LogInfo(message);
        private void LogWarn(string message) => _strategy.LogWarn(message);
        private void LogError(string message) => _strategy.LogError(message);
        private void LogDebug(string message)
        {
            if (_config.EnableDebugLogging)
                _strategy.LogInfo($"[DEBUG] {message}");
        }

        /// <summary>
        /// Get timeframe-adjusted data age threshold for validation
        /// </summary>
        private double GetTimeframeAdjustedThreshold(double baseThresholdSeconds)
        {
            try
            {
                // Get timeframe from strategy if available
                if (_strategy is OrderFlowMasterV1Strategy mainStrategy)
                {
                    return mainStrategy.GetMaxDataAgeForTimeframe(baseThresholdSeconds);
                }

                // Fallback to base threshold if strategy not available
                LogDebug($"🔍 Strategy not available for timeframe detection - using base threshold {baseThresholdSeconds:F1}s");
                return baseThresholdSeconds;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error getting timeframe-adjusted threshold: {ex.Message}");
                return baseThresholdSeconds; // Safe fallback
            }
        }

        /// <summary>
        /// Trigger emergency shutdown when fake/synthetic data is detected
        /// CRITICAL: This method immediately stops the strategy to prevent live trading with non-real data
        /// </summary>
        public void TriggerEmergencyShutdown(string reason, int bar)
        {
            LogError($"🚨 EMERGENCY SHUTDOWN TRIGGERED: {reason} at bar {bar}");
            LogError($"🚨 LIVE TRADING SAFETY: Strategy will be stopped immediately");

            try
            {
                // Set emergency shutdown flag
                _emergencyShutdownTriggered = true;

                // Stop the strategy immediately
                if (_strategy is OrderFlowMasterV1Strategy mainStrategy)
                {
                    mainStrategy.TriggerEmergencyStop(reason, bar);
                }

                // Log critical safety message
                LogError($"🚨 CRITICAL: OrderFlowMasterV1 strategy stopped due to data integrity violation");
                LogError($"🚨 REASON: {reason} - Only 100% real market data is allowed for live trading");
            }
            catch (Exception ex)
            {
                LogError($"❌ Error during emergency shutdown: {ex.Message}");
            }
        }

        /// <summary>
        /// Trigger data validation failure when synthetic data is detected
        /// </summary>
        public void TriggerDataValidationFailure(string dataType, int bar)
        {
            LogError($"❌ DATA VALIDATION FAILURE: {dataType} at bar {bar}");
            _dataValidationFailures++;

            // If too many validation failures, trigger emergency shutdown
            if (_dataValidationFailures > 3)
            {
                TriggerEmergencyShutdown($"MULTIPLE_DATA_VALIDATION_FAILURES_{dataType}", bar);
            }
        }

        #endregion

        #region Private Fields for Emergency Handling

        private bool _emergencyShutdownTriggered = false;
        private int _dataValidationFailures = 0;

        /// <summary>
        /// Check if emergency shutdown has been triggered
        /// </summary>
        public bool IsEmergencyShutdownTriggered => _emergencyShutdownTriggered;

        #endregion

        #region Disposal

        public void Dispose()
        {
            _latencyMonitor?.Dispose();
            
            lock (_dataLock)
            {
                _tickDataCache.Clear();
                _orderBookCache.Clear();
                _latencyMeasurements.Clear();
            }
            
            LogInfo("🗑️ Real Data Access Layer disposed");
        }

        #endregion

        #region Properties

        public bool IsPrimaryDataFeedActive => _primaryDataFeedActive;
        public TimeSpan AverageLatency => _averageLatency;
        public int TickDataQualityScore => _tickDataQualityScore;
        public int OrderBookQualityScore => _orderBookQualityScore;

        #endregion
    }
}
