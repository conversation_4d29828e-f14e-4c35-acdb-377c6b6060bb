using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using ATAS.Strategies.Chart;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;
using Utils.Common.Logging;

namespace OrderFlowMasterV1.Services
{
    /// <summary>
    /// Service for logging indicator data and analysis results to custom folder
    /// Ensures all data comes from REAL LIVE MARKET DATA only
    /// </summary>
    public class DataLoggingService : IDisposable
    {
        #region Private Fields

        private readonly ChartStrategy _strategy;
        private readonly OrderFlowConfiguration _config;
        private readonly string _logFolder;
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        // File writers for different data types
        private StreamWriter _signalWriter;
        private StreamWriter _footprintWriter;
        private StreamWriter _cvdWriter;
        private StreamWriter _volumeProfileWriter;
        private StreamWriter _performanceWriter;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the data logging service
        /// </summary>
        /// <param name="strategy">Chart strategy instance for LIVE market data access</param>
        /// <param name="config">Configuration settings</param>
        public DataLoggingService(ChartStrategy strategy, OrderFlowConfiguration config)
        {
            _strategy = strategy ?? throw new ArgumentNullException(nameof(strategy));
            _config = config ?? throw new ArgumentNullException(nameof(config));

            // Determine log folder path
            _logFolder = GetLogFolderPath();

            // Always show initialization status
            _strategy.LogInfo($"📁 DataLoggingService initialized - Output folder: {_logFolder}");

            // Initialize logging if enabled
            if (_config.EnableDataLogging)
            {
                InitializeLogFiles();
                _strategy.LogInfo($"🔴 LIVE MARKET DATA LOGGING ENABLED - No simulated data will be logged");
                _strategy.LogInfo($"📊 Log files will be created in: {_logFolder}");
            }
            else
            {
                _strategy.LogInfo($"📊 Data Logging: DISABLED (Enable in strategy parameters to create CSV files)");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Log order flow signal data from LIVE market
        /// </summary>
        /// <param name="signal">Signal generated from real market data</param>
        /// <param name="bar">Current bar index</param>
        public void LogSignal(OrderFlowSignal signal, int bar)
        {
            // CRITICAL FIX: Check disposal state and writer availability before logging
            if (!_config.EnableDataLogging || signal == null || _disposed || _signalWriter == null)
                return;

            try
            {
                lock (_lockObject)
                {
                    // Double-check disposal state inside lock to prevent race conditions
                    if (_disposed || _signalWriter == null)
                        return;

                    // CRITICAL FIX: Check if the StreamWriter is still writable
                    if (_signalWriter.BaseStream == null || !_signalWriter.BaseStream.CanWrite)
                        return;

                    var timestamp = DateTime.UtcNow;
                    var line = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] SIGNAL Bar={bar} Direction={signal.Direction} " +
                              $"Price={signal.Price:F5} Confluence={signal.ConfluenceScore:F2}% " +
                              $"Strength={signal.SignalStrength:F2} SL={signal.StopLossPrice:F5} " +
                              $"TP={signal.TakeProfitPrice:F5} ATR={signal.ATRValue:F5} Source=LIVE_MARKET_DATA";

                    _signalWriter.WriteLine(line);
                    _signalWriter.Flush();
                }
            }
            catch (ObjectDisposedException)
            {
                // StreamWriter was disposed - silently ignore to prevent spam
                return;
            }
            catch (InvalidOperationException)
            {
                // StreamWriter is closed - silently ignore to prevent spam
                return;
            }
            catch (Exception ex)
            {
                // Only log unexpected errors
                _strategy?.LogError($"❌ Error logging signal data: {ex.Message}");
            }
        }

        /// <summary>
        /// Log footprint data from LIVE market with momentum acceleration data
        /// CRITICAL FIX: Enhanced logging to include momentum acceleration data for debugging
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <param name="deltaValue">Delta value from real market data</param>
        /// <param name="volume">Volume from real market data</param>
        /// <param name="absorption">Absorption detected from real market data</param>
        /// <param name="momentumAcceleration">Momentum acceleration rate</param>
        /// <param name="hasMomentumBuilding">True if momentum is building</param>
        /// <param name="breakoutMomentum">Breakout momentum strength</param>
        /// <param name="institutionalMomentum">Institutional momentum strength</param>
        public void LogFootprintData(int bar, decimal deltaValue, decimal volume, bool absorption,
            decimal momentumAcceleration = 0m, bool hasMomentumBuilding = false,
            decimal breakoutMomentum = 0m, decimal institutionalMomentum = 0m)
        {
            // CRITICAL FIX: Check disposal state and writer availability before logging
            if (!_config.EnableDataLogging || _disposed || _footprintWriter == null)
                return;

            try
            {
                lock (_lockObject)
                {
                    // Double-check disposal state inside lock to prevent race conditions
                    if (_disposed || _footprintWriter == null)
                        return;

                    // CRITICAL FIX: Check if the StreamWriter is still writable
                    if (_footprintWriter.BaseStream == null || !_footprintWriter.BaseStream.CanWrite)
                        return;

                    var timestamp = DateTime.UtcNow;
                    var line = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] FOOTPRINT Bar={bar} Delta={deltaValue:F2} Volume={volume:F0} Absorption={absorption}";

                    // Add momentum data if available
                    if (momentumAcceleration != 0m || hasMomentumBuilding || breakoutMomentum != 0m || institutionalMomentum != 0m)
                    {
                        line += $" MomAccel={momentumAcceleration:F3} Building={hasMomentumBuilding} " +
                               $"Breakout={breakoutMomentum:F2} Institutional={institutionalMomentum:F2}";
                    }

                    _footprintWriter.WriteLine(line);
                    _footprintWriter.Flush();
                }
            }
            catch (ObjectDisposedException)
            {
                // StreamWriter was disposed - silently ignore to prevent spam
                return;
            }
            catch (InvalidOperationException)
            {
                // StreamWriter is closed - silently ignore to prevent spam
                return;
            }
            catch (Exception ex)
            {
                // Only log unexpected errors
                _strategy?.LogError($"❌ Error logging footprint data: {ex.Message}");
            }
        }

        /// <summary>
        /// Backward compatibility overload for LogFootprintData
        /// </summary>
        public void LogFootprintData(int bar, decimal deltaValue, decimal volume, bool absorption)
        {
            LogFootprintData(bar, deltaValue, volume, absorption, 0m, false, 0m, 0m);
        }

        /// <summary>
        /// Log CVD data from LIVE market with momentum enhancement data
        /// CRITICAL FIX: Enhanced logging to include momentum data for debugging
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <param name="cvdValue">CVD value from real market data</param>
        /// <param name="divergence">Divergence detected from real market data</param>
        /// <param name="shortTermMomentum">Short-term momentum (5-bar)</param>
        /// <param name="mediumTermMomentum">Medium-term momentum (15-bar)</param>
        /// <param name="longTermMomentum">Long-term momentum (30-bar)</param>
        /// <param name="momentumAlignment">Momentum alignment score</param>
        /// <param name="momentumAcceleration">Momentum acceleration rate</param>
        public void LogCVDData(int bar, decimal cvdValue, bool divergence,
            decimal shortTermMomentum = 0m, decimal mediumTermMomentum = 0m, decimal longTermMomentum = 0m,
            decimal momentumAlignment = 0m, decimal momentumAcceleration = 0m)
        {
            // CRITICAL FIX: Check disposal state and writer availability before logging
            if (!_config.EnableDataLogging || _disposed || _cvdWriter == null)
                return;

            try
            {
                lock (_lockObject)
                {
                    // Double-check disposal state inside lock to prevent race conditions
                    if (_disposed || _cvdWriter == null)
                        return;

                    // CRITICAL FIX: Check if the StreamWriter is still writable
                    if (_cvdWriter.BaseStream == null || !_cvdWriter.BaseStream.CanWrite)
                        return;

                    var timestamp = DateTime.UtcNow;
                    var line = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] CVD Bar={bar} Value={cvdValue:F2} Divergence={divergence}";

                    // Add momentum data if available
                    if (shortTermMomentum != 0m || mediumTermMomentum != 0m || longTermMomentum != 0m)
                    {
                        line += $" STM={shortTermMomentum:F2}% MTM={mediumTermMomentum:F2}% LTM={longTermMomentum:F2}% " +
                               $"Align={momentumAlignment:F1}% Accel={momentumAcceleration:F3}";
                    }

                    _cvdWriter.WriteLine(line);
                    _cvdWriter.Flush();
                }
            }
            catch (ObjectDisposedException)
            {
                // StreamWriter was disposed - silently ignore to prevent spam
                return;
            }
            catch (InvalidOperationException)
            {
                // StreamWriter is closed - silently ignore to prevent spam
                return;
            }
            catch (Exception ex)
            {
                // Only log unexpected errors
                _strategy?.LogError($"❌ Error logging CVD data: {ex.Message}");
            }
        }

        /// <summary>
        /// Backward compatibility overload for LogCVDData
        /// </summary>
        public void LogCVDData(int bar, decimal cvdValue, bool divergence)
        {
            LogCVDData(bar, cvdValue, divergence, 0m, 0m, 0m, 0m, 0m);
        }

        /// <summary>
        /// Log volume profile data from LIVE market
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <param name="poc">Point of Control from real market data</param>
        /// <param name="highVolumeNode">High volume node detected</param>
        public void LogVolumeProfileData(int bar, decimal poc, bool highVolumeNode)
        {
            // CRITICAL FIX: Check disposal state and writer availability before logging
            if (!_config.EnableDataLogging || _disposed || _volumeProfileWriter == null)
                return;

            try
            {
                lock (_lockObject)
                {
                    // Double-check disposal state inside lock to prevent race conditions
                    if (_disposed || _volumeProfileWriter == null)
                        return;

                    // CRITICAL FIX: Check if the StreamWriter is still writable
                    if (_volumeProfileWriter.BaseStream == null || !_volumeProfileWriter.BaseStream.CanWrite)
                        return;

                    var timestamp = DateTime.UtcNow;
                    var line = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] VOLUME_PROFILE Bar={bar} POC={poc:F5} HighVolumeNode={highVolumeNode}";

                    _volumeProfileWriter.WriteLine(line);
                    _volumeProfileWriter.Flush();
                }
            }
            catch (ObjectDisposedException)
            {
                // StreamWriter was disposed - silently ignore to prevent spam
                return;
            }
            catch (InvalidOperationException)
            {
                // StreamWriter is closed - silently ignore to prevent spam
                return;
            }
            catch (Exception ex)
            {
                // Only log unexpected errors
                _strategy?.LogError($"❌ Error logging volume profile data: {ex.Message}");
            }
        }

        /// <summary>
        /// Log performance metrics with enhanced component-specific data
        /// CRITICAL FIX: Enhanced logging to include component-specific performance metrics
        /// </summary>
        /// <param name="component">Component name</param>
        /// <param name="executionTimeMs">Execution time in milliseconds</param>
        /// <param name="bar">Current bar</param>
        /// <param name="additionalMetrics">Additional component-specific metrics</param>
        public void LogPerformance(string component, double executionTimeMs, int bar, string additionalMetrics = null)
        {
            // CRITICAL FIX: Check disposal state and writer availability before logging
            if (!_config.EnableDataLogging || _disposed || _performanceWriter == null)
                return;

            try
            {
                lock (_lockObject)
                {
                    // Double-check disposal state inside lock to prevent race conditions
                    if (_disposed || _performanceWriter == null)
                        return;

                    // CRITICAL FIX: Check if the StreamWriter is still writable
                    if (_performanceWriter.BaseStream == null || !_performanceWriter.BaseStream.CanWrite)
                        return;

                    var timestamp = DateTime.UtcNow;
                    var line = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] PERFORMANCE Component={component} Bar={bar} ExecutionTime={executionTimeMs:F3}ms";

                    // Add component-specific metrics if available
                    if (!string.IsNullOrEmpty(additionalMetrics))
                    {
                        line += $" {additionalMetrics}";
                    }

                    _performanceWriter.WriteLine(line);
                    _performanceWriter.Flush();
                }
            }
            catch (ObjectDisposedException)
            {
                // StreamWriter was disposed - silently ignore to prevent spam
                return;
            }
            catch (InvalidOperationException)
            {
                // StreamWriter is closed - silently ignore to prevent spam
                return;
            }
            catch (Exception ex)
            {
                // Only log unexpected errors
                _strategy?.LogError($"❌ Error logging performance data: {ex.Message}");
            }
        }

        /// <summary>
        /// Backward compatibility overload for LogPerformance
        /// </summary>
        public void LogPerformance(string component, double executionTimeMs, int bar)
        {
            LogPerformance(component, executionTimeMs, bar, null);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Get the log folder path based on configuration
        /// </summary>
        private string GetLogFolderPath()
        {
            string baseFolder;

            if (!string.IsNullOrWhiteSpace(_config.LogOutputFolder))
            {
                // Use custom folder if specified
                baseFolder = _config.LogOutputFolder;
            }
            else
            {
                // Use default ATAS folder
                baseFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "ATAS", "Logs", "OrderFlowMaster"
                );
            }

            // Create timestamped subfolder for this session
            var sessionFolder = Path.Combine(baseFolder, DateTime.Now.ToString("yyyy-MM-dd"));
            
            try
            {
                Directory.CreateDirectory(sessionFolder);
            }
            catch (Exception ex)
            {
                _strategy?.LogError($"❌ Failed to create log directory {sessionFolder}: {ex.Message}");
                // Fallback to temp folder
                sessionFolder = Path.Combine(Path.GetTempPath(), "OrderFlowMaster", DateTime.Now.ToString("yyyy-MM-dd"));
                Directory.CreateDirectory(sessionFolder);
            }

            return sessionFolder;
        }

        /// <summary>
        /// Initialize log files with headers
        /// </summary>
        private void InitializeLogFiles()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("HHmmss");

                // Initialize signal log
                var signalPath = Path.Combine(_logFolder, $"signals_{timestamp}.log");
                _signalWriter = new StreamWriter(signalPath, false, Encoding.UTF8);
                _signalWriter.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] SIGNAL LOG INITIALIZED");

                // Initialize footprint log
                var footprintPath = Path.Combine(_logFolder, $"footprint_{timestamp}.log");
                _footprintWriter = new StreamWriter(footprintPath, false, Encoding.UTF8);
                _footprintWriter.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] FOOTPRINT LOG INITIALIZED");

                // Initialize CVD log
                var cvdPath = Path.Combine(_logFolder, $"cvd_{timestamp}.log");
                _cvdWriter = new StreamWriter(cvdPath, false, Encoding.UTF8);
                _cvdWriter.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] CVD LOG INITIALIZED");

                // Initialize volume profile log
                var vpPath = Path.Combine(_logFolder, $"volume_profile_{timestamp}.log");
                _volumeProfileWriter = new StreamWriter(vpPath, false, Encoding.UTF8);
                _volumeProfileWriter.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] VOLUME PROFILE LOG INITIALIZED");

                // Initialize performance log
                var perfPath = Path.Combine(_logFolder, $"performance_{timestamp}.log");
                _performanceWriter = new StreamWriter(perfPath, false, Encoding.UTF8);
                _performanceWriter.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] PERFORMANCE LOG INITIALIZED");

                _strategy?.LogInfo($"📊 Log files initialized in: {_logFolder}");
            }
            catch (Exception ex)
            {
                _strategy?.LogError($"❌ Failed to initialize log files: {ex.Message}");
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            lock (_lockObject)
            {
                if (!_disposed)
                {
                    try
                    {
                        // CRITICAL FIX: Set disposed flag first to prevent new writes
                        _disposed = true;

                        // Dispose all writers safely
                        _signalWriter?.Dispose();
                        _signalWriter = null;

                        _footprintWriter?.Dispose();
                        _footprintWriter = null;

                        _cvdWriter?.Dispose();
                        _cvdWriter = null;

                        _volumeProfileWriter?.Dispose();
                        _volumeProfileWriter = null;

                        _performanceWriter?.Dispose();
                        _performanceWriter = null;
                    }
                    catch (Exception ex)
                    {
                        // Use Console.WriteLine to avoid potential recursion if _strategy is also disposing
                        Console.WriteLine($"❌ Error disposing log writers: {ex.Message}");
                    }
                }
            }
        }

        #endregion
    }
}
