using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// Unit tests for LatencyCompensator - Phase 3 Ultra-Precision Component
    /// Validates Australian geographical latency compensation for crypto futures trading
    /// </summary>
    [TestClass]
    public class LatencyCompensatorTests
    {
        private LatencyCompensator _latencyCompensator;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private TestChartStrategy _mockStrategy;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = true
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                TickSize = 0.01m,
                MinimumVolume = 0.001m
            };

            _mockStrategy = new TestChartStrategy();
            _latencyCompensator = new LatencyCompensator(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _latencyCompensator?.Dispose();
        }

        [TestMethod]
        public void LatencyCompensator_Initialization_ShouldSucceed()
        {
            // Arrange & Act - Done in Setup

            // Assert
            Assert.IsNotNull(_latencyCompensator);
            Assert.IsNotNull(_config);
            Assert.IsNotNull(_assetConfig);
        }

        [TestMethod]
        public void CompensateStopDistance_WithBaseParameters_ShouldIncreaseStopDistance()
        {
            // Arrange
            var originalStopDistance = 0.003m; // 0.30%
            var currentVolatility = 0.002m; // 0.20%
            var bar = 10;

            // Act
            var compensatedStopDistance = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, currentVolatility, bar);

            // Assert
            Assert.IsTrue(compensatedStopDistance > originalStopDistance);
            Assert.IsTrue(compensatedStopDistance <= originalStopDistance + 0.001m); // Max 0.1% compensation
        }

        [TestMethod]
        public void CompensateStopDistance_WithHighVolatility_ShouldApplyLargerCompensation()
        {
            // Arrange
            var originalStopDistance = 0.003m; // 0.30%
            var lowVolatility = 0.001m; // 0.10%
            var highVolatility = 0.005m; // 0.50%
            var bar = 15;

            // Act
            var lowVolCompensation = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, lowVolatility, bar);
            var highVolCompensation = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, highVolatility, bar + 1);

            // Assert
            Assert.IsTrue(highVolCompensation > lowVolCompensation);
            Assert.IsTrue(highVolCompensation > originalStopDistance);
        }

        [TestMethod]
        public void CompensateTakeProfitDistance_WithBaseParameters_ShouldIncreaseTPDistance()
        {
            // Arrange
            var originalTPDistance = 0.004m; // 0.40%
            var currentVolatility = 0.002m; // 0.20%
            var bar = 20;

            // Act
            var compensatedTPDistance = _latencyCompensator.CompensateTakeProfitDistance(
                originalTPDistance, currentVolatility, bar);

            // Assert
            Assert.IsTrue(compensatedTPDistance > originalTPDistance);
            // TP compensation should be smaller than stop compensation
            Assert.IsTrue(compensatedTPDistance <= originalTPDistance + 0.0005m); // Max 0.05% compensation
        }

        [TestMethod]
        public void UpdateLatencyMeasurement_WithNormalLatency_ShouldUpdateStatistics()
        {
            // Arrange
            var normalLatency = 8.0m; // 8ms - normal for Australia

            // Act
            _latencyCompensator.UpdateLatencyMeasurement(normalLatency);
            var stats = _latencyCompensator.GetLatencyStatistics();

            // Assert
            Assert.AreEqual(normalLatency, stats.CurrentLatencyMs);
            Assert.IsTrue(stats.MeasurementCount > 0);
            Assert.IsFalse(stats.LatencySpikes > 0); // Should not be a spike
        }

        [TestMethod]
        public void UpdateLatencyMeasurement_WithLatencySpike_ShouldDetectSpike()
        {
            // Arrange
            var normalLatency = 7.5m; // Base latency
            var spikeLatency = 20.0m; // High latency spike

            // Act
            _latencyCompensator.UpdateLatencyMeasurement(normalLatency);
            _latencyCompensator.UpdateLatencyMeasurement(spikeLatency);
            var stats = _latencyCompensator.GetLatencyStatistics();

            // Assert
            Assert.AreEqual(spikeLatency, stats.CurrentLatencyMs);
            Assert.IsTrue(stats.MaxLatencyMs >= spikeLatency);
            Assert.IsTrue(stats.LatencySpikes > 0); // Should detect spike
        }

        [TestMethod]
        public void GetLatencyStatistics_WithMultipleMeasurements_ShouldCalculateCorrectAverages()
        {
            // Arrange
            var latencies = new[] { 6.0m, 7.0m, 8.0m, 9.0m, 10.0m };
            var expectedAverage = 8.0m;

            // Act
            foreach (var latency in latencies)
            {
                _latencyCompensator.UpdateLatencyMeasurement(latency);
            }
            var stats = _latencyCompensator.GetLatencyStatistics();

            // Assert
            Assert.AreEqual(latencies.Length, stats.MeasurementCount);
            Assert.AreEqual(expectedAverage, stats.AverageLatencyMs);
            Assert.AreEqual(6.0m, stats.MinLatencyMs);
            Assert.AreEqual(10.0m, stats.MaxLatencyMs);
        }

        [TestMethod]
        public void CompensateStopDistance_PerformanceTest_ShouldExecuteWithinTimeLimit()
        {
            // Arrange
            var originalStopDistance = 0.003m;
            var currentVolatility = 0.002m;
            var bar = 25;
            var maxExecutionTimeMs = 0.5; // 0.5ms target (should be <0.3ms per spec)

            // Act
            var startTime = DateTime.UtcNow;
            var result = _latencyCompensator.CompensateStopDistance(originalStopDistance, currentVolatility, bar);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(result > originalStopDistance);
            Assert.IsTrue(executionTime < maxExecutionTimeMs,
                $"Execution time {executionTime:F2}ms exceeds target {maxExecutionTimeMs}ms");
        }

        [TestMethod]
        public void CompensateStopDistance_MultipleConsecutiveCalls_ShouldMaintainPerformance()
        {
            // Arrange
            var iterations = 50;
            var maxAverageTimeMs = 0.5;
            var totalTime = 0.0;
            var originalStopDistance = 0.003m;
            var currentVolatility = 0.002m;

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var bar = 30 + i;
                var startTime = DateTime.UtcNow;
                var result = _latencyCompensator.CompensateStopDistance(originalStopDistance, currentVolatility, bar);
                var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

                totalTime += executionTime;

                // Assert each result is valid
                Assert.IsTrue(result > originalStopDistance);
            }

            // Assert average performance
            var averageTime = totalTime / iterations;
            Assert.IsTrue(averageTime < maxAverageTimeMs,
                $"Average execution time {averageTime:F2}ms exceeds target {maxAverageTimeMs}ms");
        }

        [TestMethod]
        public void CompensateStopDistance_WithMaximumCompensation_ShouldNotExceedLimit()
        {
            // Arrange
            var originalStopDistance = 0.003m; // 0.30%
            var extremeVolatility = 0.1m; // 10% volatility (extreme)
            var bar = 35;

            // Simulate high latency
            _latencyCompensator.UpdateLatencyMeasurement(50.0m); // Very high latency

            // Act
            var compensatedStopDistance = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, extremeVolatility, bar);

            // Assert
            var maxExpectedCompensation = originalStopDistance + 0.001m; // 0.1% max compensation
            Assert.IsTrue(compensatedStopDistance <= maxExpectedCompensation);
        }

        [TestMethod]
        public void CompensateStopDistance_WithZeroVolatility_ShouldStillApplyLatencyCompensation()
        {
            // Arrange
            var originalStopDistance = 0.003m; // 0.30%
            var zeroVolatility = 0.0m;
            var bar = 40;

            // Act
            var compensatedStopDistance = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, zeroVolatility, bar);

            // Assert
            Assert.IsTrue(compensatedStopDistance > originalStopDistance);
            // Should still apply base latency compensation even with zero volatility
        }

        [TestMethod]
        public void CompensateTakeProfitDistance_ShouldApplySmallerCompensationThanStop()
        {
            // Arrange
            var originalStopDistance = 0.003m; // 0.30%
            var originalTPDistance = 0.004m; // 0.40%
            var currentVolatility = 0.002m; // 0.20%
            var bar = 45;

            // Act
            var compensatedStopDistance = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, currentVolatility, bar);
            var compensatedTPDistance = _latencyCompensator.CompensateTakeProfitDistance(
                originalTPDistance, currentVolatility, bar);

            // Assert
            var stopCompensation = compensatedStopDistance - originalStopDistance;
            var tpCompensation = compensatedTPDistance - originalTPDistance;
            
            Assert.IsTrue(tpCompensation < stopCompensation);
            Assert.IsTrue(compensatedTPDistance > originalTPDistance);
        }

        [TestMethod]
        public void GetLatencyStatistics_WithNoMeasurements_ShouldReturnDefaultValues()
        {
            // Arrange - Fresh compensator with no measurements

            // Act
            var stats = _latencyCompensator.GetLatencyStatistics();

            // Assert
            Assert.AreEqual(7.5m, stats.CurrentLatencyMs); // Base latency
            Assert.AreEqual(7.5m, stats.AverageLatencyMs); // Base latency
            Assert.AreEqual(0, stats.LatencySpikes);
            Assert.AreEqual(0, stats.MeasurementCount);
        }

        [TestMethod]
        public void LatencyCompensator_AustralianEnvironmentOptimization_ShouldTargetCorrectLatencyRange()
        {
            // Arrange
            var targetMinLatency = 5.0m; // 5ms minimum expected
            var targetMaxLatency = 10.0m; // 10ms maximum acceptable
            var originalStopDistance = 0.003m;
            var currentVolatility = 0.002m;
            var bar = 50;

            // Simulate typical Australian latency
            _latencyCompensator.UpdateLatencyMeasurement(7.5m);

            // Act
            var compensatedStopDistance = _latencyCompensator.CompensateStopDistance(
                originalStopDistance, currentVolatility, bar);
            var stats = _latencyCompensator.GetLatencyStatistics();

            // Assert
            Assert.IsTrue(stats.CurrentLatencyMs >= targetMinLatency);
            Assert.IsTrue(stats.CurrentLatencyMs <= targetMaxLatency);
            Assert.IsTrue(compensatedStopDistance > originalStopDistance);
            
            // Compensation should be reasonable for Australian environment
            var compensation = compensatedStopDistance - originalStopDistance;
            Assert.IsTrue(compensation <= 0.0005m); // Max 0.05% reasonable compensation
        }
    }
}
