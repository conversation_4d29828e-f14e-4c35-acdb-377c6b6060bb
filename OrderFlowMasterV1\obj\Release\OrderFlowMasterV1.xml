<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OrderFlowMasterV1</name>
    </assembly>
    <members>
        <member name="T:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer">
            <summary>
            🎯 PHASE 3: Adaptive Parameter Optimizer for real-time strategy optimization
            
            This component implements machine learning-based parameter optimization that adapts
            strategy parameters in real-time based on market conditions and performance feedback.
            Targets 15% performance improvement through intelligent parameter adjustment.
            
            Key Features:
            - Real-time parameter adjustment based on market conditions
            - Machine learning optimization algorithms
            - Performance feedback integration
            - Market regime-aware optimization
            - Less than 2ms execution time per optimization cycle
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.#ctor(ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration)">
            <summary>
            Initialize the Adaptive Parameter Optimizer
            </summary>
            <param name="strategy">Main strategy instance for logging and data access</param>
            <param name="config">Strategy configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
        </member>
        <member name="P:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.CurrentPerformanceScore">
            <summary>
            Current optimization performance score (0-1 scale)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.PerformanceImprovement">
            <summary>
            Performance improvement achieved vs baseline
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.IsOptimizationEnabled">
            <summary>
            Whether optimization is currently enabled
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.CurrentRegime">
            <summary>
            Current market regime detected by optimizer
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.OptimizationCycles">
            <summary>
            Number of optimization cycles completed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.OptimizeParameters(System.Int32,OrderFlowMasterV1.Components.MarketCondition,OrderFlowMasterV1.Components.PerformanceMetric)">
            <summary>
            🎯 PHASE 3: Perform real-time parameter optimization
            Target: Less than 2ms execution time per optimization cycle
            </summary>
            <param name="currentBar">Current bar index</param>
            <param name="marketCondition">Current market conditions</param>
            <param name="recentPerformance">Recent performance metrics</param>
            <returns>Optimization result with updated parameters</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.GetCurrentParameters">
            <summary>
            Get current optimized parameters for external use
            </summary>
            <returns>Dictionary of current parameter values</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ForceEmergencyMode(System.String)">
            <summary>
            Force emergency mode activation (for testing or manual intervention)
            </summary>
            <param name="reason">Reason for emergency mode activation</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ResetToBaseline">
            <summary>
            Reset optimizer to baseline parameters and exit emergency mode
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.InitializeBaselineParameters">
            <summary>
            Initialize baseline parameters from current configuration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.InitializeRegimeParameters">
            <summary>
            Initialize regime-specific optimal parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.DetectMarketRegime(OrderFlowMasterV1.Components.MarketCondition)">
            <summary>
            Detect current market regime based on market conditions
            </summary>
            <param name="marketCondition">Current market conditions</param>
            <returns>Detected market regime</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.PerformRegimeBasedOptimization(OrderFlowMasterV1.Components.MarketCondition,OrderFlowMasterV1.Components.PerformanceMetric)">
            <summary>
            Perform regime-based optimization using machine learning algorithms
            </summary>
            <param name="marketCondition">Current market conditions</param>
            <param name="recentPerformance">Recent performance metrics</param>
            <returns>Optimization result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ApplyMLAdjustments(OrderFlowMasterV1.Components.ParameterSet,OrderFlowMasterV1.Components.PerformanceMetric)">
            <summary>
            Apply machine learning adjustments to optimal parameters
            </summary>
            <param name="baseParams">Base optimal parameters for regime</param>
            <param name="performance">Recent performance metrics</param>
            <returns>ML-adjusted parameters</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ValidateParameterBounds(OrderFlowMasterV1.Components.ParameterSet)">
            <summary>
            Validate parameter bounds to ensure they remain within acceptable ranges
            </summary>
            <param name="parameters">Parameters to validate</param>
            <returns>Validated parameters within bounds</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.CalculateExpectedImprovement(OrderFlowMasterV1.Components.ParameterSet,OrderFlowMasterV1.Components.PerformanceMetric)">
            <summary>
            Calculate expected performance improvement from parameter changes
            </summary>
            <param name="newParams">New parameter set</param>
            <param name="currentPerformance">Current performance metrics</param>
            <returns>Expected improvement percentage</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.UpdatePerformanceTracking(OrderFlowMasterV1.Components.PerformanceMetric,OrderFlowMasterV1.Components.OptimizationResult)">
            <summary>
            Update performance tracking with new metrics
            </summary>
            <param name="performance">Recent performance metrics</param>
            <param name="optimizationResult">Optimization result</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.CalculateCompositePerformanceScore(OrderFlowMasterV1.Components.PerformanceMetric)">
            <summary>
            Calculate composite performance score from individual metrics
            </summary>
            <param name="performance">Performance metrics</param>
            <returns>Composite score (0-1 scale)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ApplyOptimizedParameters(System.Collections.Generic.Dictionary{System.String,System.Decimal})">
            <summary>
            Apply optimized parameters to current parameter set
            </summary>
            <param name="optimizedParameters">Dictionary of optimized parameters</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.EnterEmergencyMode(System.String)">
            <summary>
            Enter emergency mode and revert to safe parameters
            </summary>
            <param name="reason">Reason for emergency mode activation</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ConvertParameterSetToDictionary(OrderFlowMasterV1.Components.ParameterSet)">
            <summary>
            Convert parameter set to dictionary format
            </summary>
            <param name="parameterSet">Parameter set to convert</param>
            <returns>Dictionary representation</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.ConvertDictionaryToParameterSet(System.Collections.Generic.Dictionary{System.String,System.Decimal})">
            <summary>
            Convert dictionary to parameter set format
            </summary>
            <param name="parameters">Dictionary to convert</param>
            <returns>Parameter set representation</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.AdaptiveParameterOptimizer.Dispose">
            <summary>
            Dispose of resources
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketRegime">
            <summary>
            Market regime enumeration for optimization
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketCondition">
            <summary>
            Market condition data for regime detection
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PerformanceMetric">
            <summary>
            Performance metric for optimization feedback
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.OptimizationResult">
            <summary>
            Optimization result containing updated parameters
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.ParameterSet">
            <summary>
            Parameter set for regime-specific optimization
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.ConfluenceDetector">
            <summary>
            Confluence Detector for Multi-Indicator Analysis
            
            This component combines signals from multiple order flow indicators to identify
            high-probability trading opportunities through confluence analysis:
            
            Required Indicators for Confluence:
            1. Footprint Analysis (absorption, institutional flow)
            2. CVD Analysis (trend validation, divergence)
            3. Volume Profile (key levels, support/resistance)
            4. Delta Analysis (breakout confirmation)
            
            Unlimited Analysis Mode: No performance constraints - Complete confluence analysis guaranteed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.OrderFlowMasterV1Strategy)">
            <summary>
            Initialize the Confluence Detector (Phase 2: Enhanced with momentum indicators)
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
            <param name="mainStrategy">Main strategy instance for momentum indicators access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.DetectConfluence(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Detect confluence across all indicators for the specified bar
            </summary>
            <param name="bar">Bar index to analyze</param>
            <param name="candle">Candle data for the bar</param>
            <param name="footprintData">Footprint analysis data</param>
            <param name="cvdData">CVD analysis data</param>
            <param name="volumeProfileData">Volume profile analysis data</param>
            <returns>Confluence analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.DetectConfluence(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,OrderFlowMasterV1.Models.VolumeProfileData,OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Detect confluence across all indicators including momentum analysis (Phase 3)
            </summary>
            <param name="bar">Bar index to analyze</param>
            <param name="candle">Candle data for the bar</param>
            <param name="footprintData">Footprint analysis data</param>
            <param name="cvdData">CVD analysis data</param>
            <param name="volumeProfileData">Volume profile analysis data</param>
            <param name="momentumData">Momentum analysis data (Phase 3)</param>
            <returns>Enhanced confluence analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.DetectConfluence(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,OrderFlowMasterV1.Models.VolumeProfileData,OrderFlowMasterV1.Models.MomentumData,OrderFlowMasterV1.Components.OrderFlowVelocityResult,OrderFlowMasterV1.Components.LiquidityVacuumResult,OrderFlowMasterV1.Components.StopHuntSignal,OrderFlowMasterV1.Components.MarketMakerResult)">
            <summary>
            Detect confluence across all indicators including momentum analysis (Phase 3) and ultra-precision components (Phase 1)
            ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md integration
            </summary>
            <param name="bar">Bar index to analyze</param>
            <param name="candle">Candle data for the bar</param>
            <param name="footprintData">Footprint analysis data</param>
            <param name="cvdData">CVD analysis data</param>
            <param name="volumeProfileData">Volume profile analysis data</param>
            <param name="momentumData">Momentum analysis data (Phase 3)</param>
            <param name="velocityResult">Order flow velocity analysis (Phase 1)</param>
            <param name="vacuumResult">Liquidity vacuum analysis (Phase 1)</param>
            <param name="stopHuntSignal">Stop hunt detection signal (Phase 1)</param>
            <param name="mmResult">Market maker analysis result (Phase 1)</param>
            <returns>Ultra-precision enhanced confluence analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.HasConfluence(System.Int32)">
            <summary>
            Check if confluence threshold is met at the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if confluence threshold is met</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.GetConfluenceScore(System.Int32)">
            <summary>
            Get confluence score for the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>Confluence score (0-100%)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.GetSignalDirection(System.Int32)">
            <summary>
            Get signal direction from confluence analysis
            </summary>
            <param name="bar">Bar index</param>
            <returns>Signal direction</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScoreFootprintIndicator(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Score footprint indicator contribution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScoreCVDIndicator(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.CVDData)">
            <summary>
            Score CVD indicator contribution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScoreVolumeProfileIndicator(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Score volume profile indicator contribution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScoreDeltaIndicator(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Score delta indicator contribution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScoreMomentumIndicators(OrderFlowMasterV1.Models.ConfluenceResult,System.Int32,OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Phase 2/3: Score momentum indicators contribution using ATAS momentum indicators
            Enhanced in Phase 3 to use dedicated MomentumDetector data when available
            Combines ROC, Volume Oscillator, and MACD for comprehensive momentum analysis
            </summary>
            <param name="result">Confluence result to update</param>
            <param name="bar">Bar index for momentum calculation</param>
            <param name="momentumData">Dedicated momentum data from Phase 3 MomentumDetector (optional)</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateOverallConfluence(OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            🎯 ENHANCED: Calculate overall confluence score with dynamic threshold optimization
            Priority 3: Implements volatility-adjusted thresholds and adaptive signal quality scoring
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateAdaptiveConfluenceThreshold(OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            📊 PRIORITY 3: Calculate adaptive confluence threshold based on market volatility
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateVolatilityAdjustment(System.Int32)">
            <summary>
            📈 Calculate volatility adjustment factor for dynamic thresholds
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.ScorePhase1UltraPrecisionComponents(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Components.OrderFlowVelocityResult,OrderFlowMasterV1.Components.LiquidityVacuumResult,OrderFlowMasterV1.Components.StopHuntSignal,OrderFlowMasterV1.Components.MarketMakerResult)">
            <summary>
            🚀 Score Phase 1 Ultra-Precision Components (ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md)
            Integrates velocity spikes, liquidity vacuums, stop hunts, and MM withdrawal signals
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateMarketConditionMultiplier(System.Int32)">
            <summary>
            🌍 Calculate market condition multiplier for Australian trading environment
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateRecentAverageVolume(System.Int32,System.Int32)">
            <summary>
            📊 Calculate recent average volume for market condition analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.DetermineSignalDirection(OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData)">
            <summary>
            Determine signal direction from confluence analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CalculateQualityScore(OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            Calculate quality score for the confluence analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.GenerateAnalysisSummary(OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            Generate analysis summary
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.CleanupHistory(System.Int32)">
            <summary>
            Clean up old historical data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.ConfluenceDetector.GetHistoricalCandle(System.Int32)">
            <summary>
            📊 Get historical candle data for analysis
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.CVDCalculator">
            <summary>
            Cumulative Volume Delta (CVD) Calculator for BTC/ETH Futures
            
            This component tracks the cumulative difference between buying and selling volume
            over time to identify institutional flow patterns, trend validation, and divergences.
            
            Key Features:
            - Real-time CVD calculation and tracking
            - Trend validation and momentum analysis
            - Divergence detection between price and CVD
            - Session-based reset capabilities
            - Institutional flow strength measurement
            
            Performance Target: Less than 1ms execution time
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the CVD Calculator
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetHistoricalCandle(System.Int32)">
            <summary>
            Get historical candle data (wrapper for strategy GetCandle)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCVD(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Calculate CVD for the specified bar
            </summary>
            <param name="bar">Bar index to calculate</param>
            <param name="candle">Candle data for the bar</param>
            <param name="footprintData">Footprint data for delta information</param>
            <returns>CVD analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetCurrentCVD">
            <summary>
            Get current CVD value
            </summary>
            <returns>Current cumulative delta value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetSessionCVD">
            <summary>
            Get session CVD value
            </summary>
            <returns>Session cumulative delta value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetCVDTrend(System.Int32)">
            <summary>
            Get CVD trend direction
            </summary>
            <param name="bar">Bar index</param>
            <returns>CVD trend direction</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.HasCVDDivergence(System.Int32)">
            <summary>
            Check if CVD divergence exists at the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if divergence detected</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.ResetCVD">
            <summary>
            Reset CVD calculation (useful for new sessions)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetMTFCorrelation(System.Int32)">
            <summary>
            ENHANCED: Get multi-timeframe CVD correlation data (Phase 2)
            </summary>
            <param name="bar">Bar index</param>
            <returns>Multi-timeframe correlation data or null if not available</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.HasMTFValidation(System.Int32)">
            <summary>
            ENHANCED: Check if multi-timeframe validation is available for a bar (Phase 2)
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if MTF validation is available and correlation > threshold</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCVDTrend(OrderFlowMasterV1.Models.CVDData,System.Int32)">
            <summary>
            Calculate CVD trend direction and strength
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCVDMomentum(OrderFlowMasterV1.Models.CVDData,System.Int32)">
            <summary>
            ENHANCED: Calculate CVD momentum with multi-timeframe analysis (Phase 1)
            Implements 5-bar, 15-bar, and 30-bar momentum calculations with alignment scoring
            Performance target: less than 1.5ms additional overhead using circular buffers
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateMultiTimeframeMomentum(OrderFlowMasterV1.Models.CVDData,System.Int32,System.Decimal)">
            <summary>
            ENHANCED: Calculate multi-timeframe momentum analysis (Phase 1)
            Implements 5-bar, 15-bar, and 30-bar momentum with alignment scoring and acceleration detection
            Uses circular buffers for optimal performance (less than 1.5ms target)
            100% real live market data compliance required
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateMomentum(System.Int32,System.Int32,System.Decimal)">
            <summary>
            Calculate momentum for a specific period using incremental calculation
            Optimized for performance with caching
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateAlignmentScore(System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Calculate momentum alignment score (0-100 scale)
            Research-backed approach: aligned momentum provides stronger signals
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateMomentumAcceleration(System.Int32)">
            <summary>
            Calculate momentum acceleration using circular buffer for performance
            Detects building momentum patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.ValidateDataSource(System.String,System.Int32)">
            <summary>
            Validate data source for 100% real live market data compliance
            Emergency shutdown trigger for synthetic data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            Trigger emergency shutdown for data integrity violations
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.DetectCVDDivergence(OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            🚀 ENHANCED CVD MOMENTUM DETECTION: Professional trading analysis with 5-minute slope analysis
            Implements latest research-backed techniques for institutional flow reversal detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCVDMomentumSlope(System.Int32,System.Int32)">
            <summary>
            📈 Calculate CVD momentum slope over specified period (Professional Analysis)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculatePriceMomentumSlope(System.Int32,System.Int32)">
            <summary>
            📊 Calculate price momentum slope over specified period
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateLinearRegressionSlope(System.Collections.Generic.List{System.Decimal})">
            <summary>
            🧮 Calculate linear regression slope for momentum analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateInstitutionalFlowScore(OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            🏦 Calculate institutional flow score based on order flow patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.DetectTraditionalDivergence(OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            📊 Detect traditional divergence patterns for comparison
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.DetermineDivergenceType(System.Decimal,System.Decimal,OrderFlowMasterV1.Models.DivergenceType)">
            <summary>
            🎯 Determine divergence type based on slope analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateEnhancedDivergenceStrength(System.Decimal,System.Decimal,System.Decimal,System.Boolean)">
            <summary>
            Calculate enhanced divergence strength with multi-factor scoring
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.AnalyzeInstitutionalFlow(OrderFlowMasterV1.Models.CVDData,OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Analyze institutional flow through CVD patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCVDStrength(OrderFlowMasterV1.Models.CVDData,System.Int32)">
            <summary>
            Calculate overall CVD strength
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateConfidence(OrderFlowMasterV1.Models.CVDData,System.Int32)">
            <summary>
            Calculate confidence score for CVD analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CheckSessionReset">
            <summary>
            Check if session reset is needed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.UpdateTrendBuffer(System.Decimal)">
            <summary>
            Update trend buffer for trend analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CleanupHistory(System.Int32)">
            <summary>
            Clean up old historical data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.AnalyzeMultiTimeframeCVDCorrelation(OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze multi-timeframe CVD correlation for signal validation
            Research-backed approach providing 30-50% false signal reduction when correlation > 0.75
            Performance: Optimized with caching for sub-millisecond execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetHigherTimeframeCVD(System.DateTime,System.TimeSpan,System.Int32)">
            <summary>
            Get higher timeframe CVD value using REAL ATAS MULTI-TIMEFRAME DATA
            ENHANCED: Replaces reconstructed data with authentic higher timeframe feeds
            Performance: Optimized with intelligent caching for less than 1ms execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateTimeframeTrend(System.Decimal,System.Collections.Generic.Dictionary{System.DateTime,System.Decimal},System.DateTime,System.TimeSpan)">
            <summary>
            Calculate trend direction for a specific timeframe
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateTimeframeMomentum(System.Decimal,System.Collections.Generic.Dictionary{System.DateTime,System.Decimal},System.TimeSpan)">
            <summary>
            Calculate momentum for a specific timeframe
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CalculateCorrelationMetrics(OrderFlowMasterV1.Models.MultiTimeframeCVDCorrelation,OrderFlowMasterV1.Models.CVDData)">
            <summary>
            Calculate correlation metrics between timeframes
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetTimeframeBoundary(System.DateTime,System.TimeSpan)">
            <summary>
            Get timeframe boundary for caching
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetBarsInTimeframe(System.TimeSpan)">
            <summary>
            Get approximate number of bars in a timeframe (assuming 1-minute bars)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.CleanupTimeframeCache(System.Collections.Generic.Dictionary{System.DateTime,System.Decimal},System.DateTime,System.TimeSpan)">
            <summary>
            Cleanup old timeframe cache entries for memory management
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.CVDCalculator.GetRealMultiTimeframeData(System.DateTime,System.TimeSpan)">
            <summary>
            CRITICAL: Get real multi-timeframe data from ATAS higher timeframe feeds
            Uses ATAS APIs for authentic higher timeframe data - NO RECONSTRUCTION ALLOWED
            Based on ATAS documentation and ChartStrategy capabilities
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.EntrySignalGenerator">
            <summary>
            Entry Signal Generator for OrderFlowMaster V1 Strategy
            
            This component generates high-quality trading signals by combining confluence
            analysis with risk management parameters. It ensures that only signals meeting
            strict quality and confluence requirements are generated for execution.
            
            Signal Generation Requirements:
            1. Confluence threshold must be met (default 75%)
            2. Minimum 3 out of 4 indicators must confirm
            3. Clear directional bias with >60% confidence
            4. Risk/reward ratio must be >1.5:1
            5. Session and time filtering applied
            
            Unlimited Analysis Mode: No performance constraints - Complete signal generation guaranteed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Entry Signal Generator
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GetHistoricalCandle(System.Int32)">
            <summary>
            Get historical candle data (wrapper for strategy GetCandle)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GenerateSignal(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.ConfluenceResult,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Generate entry signal based on confluence analysis
            </summary>
            <param name="bar">Bar index</param>
            <param name="candle">Candle data for the bar</param>
            <param name="confluenceResult">Confluence analysis result</param>
            <param name="footprintData">Footprint analysis data</param>
            <param name="cvdData">CVD analysis data</param>
            <param name="volumeProfileData">Volume profile analysis data</param>
            <returns>Generated order flow signal or null if no signal</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GetSignal(System.Int32)">
            <summary>
            Get signal for the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>Order flow signal or null if no signal</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GetStatistics">
            <summary>
            Get signal generation statistics
            </summary>
            <returns>Signal generation statistics</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.CreateBaseSignal(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            Create base signal structure
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PopulateFootprintData(OrderFlowMasterV1.Models.OrderFlowSignal,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Populate footprint analysis data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PopulateCVDData(OrderFlowMasterV1.Models.OrderFlowSignal,OrderFlowMasterV1.Models.CVDData)">
            <summary>
            Populate CVD analysis data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PopulateVolumeProfileData(OrderFlowMasterV1.Models.OrderFlowSignal,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Populate volume profile data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PopulateConfluenceData(OrderFlowMasterV1.Models.OrderFlowSignal,OrderFlowMasterV1.Models.ConfluenceResult)">
            <summary>
            Populate confluence analysis data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PassesPreFilters(OrderFlowMasterV1.Models.ConfluenceResult,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Apply pre-filters before signal generation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.CalculateRiskParameters(OrderFlowMasterV1.Models.OrderFlowSignal,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Calculate risk management parameters using strategy parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.ValidateSignalQuality(OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Validate signal quality
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.PassesFinalFilters(OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Apply final filters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.IsValidTradingSession">
            <summary>
            Check if current time is valid for trading
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.HasPassedCooldownPeriod">
            <summary>
            Check if cooldown period has passed
            FIXED: Support fractional minutes for sub-minute cooldowns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GetRecentSignalCount">
            <summary>
            Get count of recent signals
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.CalculateMarketVolatility">
            <summary>
            Calculate current market volatility
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.GetCurrentMarketSession">
            <summary>
            Get current market session
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.AssessLiquidityLevel(System.Decimal)">
            <summary>
            Assess liquidity level based on volume
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.CleanupHistory(System.Int32)">
            <summary>
            Clean up old historical data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.EntrySignalGenerator.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.FootprintEngine">
            <summary>
            Advanced Footprint Analysis Engine for BTC/ETH Futures
            
            This component provides comprehensive footprint analysis including:
            - Delta analysis and imbalance detection
            - Absorption pattern recognition
            - Institutional flow identification
            - Volume concentration analysis
            - Breakout confirmation through order flow
            
            Performance Target: Less than 2ms execution time
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Footprint Analysis Engine
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.GetHistoricalCandle(System.Int32)">
            <summary>
            Get historical candle data (wrapper for strategy GetCandle)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeFootprint(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Analyze footprint data for the specified bar with performance budgeting
            OPTIMIZED: Implements 2ms performance budget with prioritized analysis
            </summary>
            <param name="bar">Bar index to analyze</param>
            <param name="candle">Candle data for the bar</param>
            <returns>Comprehensive footprint analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.GetDelta(System.Int32)">
            <summary>
            Get delta value for the specified bar (OPTIMIZED - No recursion)
            FIXED: Eliminates recursive AnalyzeFootprint calls that caused 20-200ms performance issues
            </summary>
            <param name="bar">Bar index</param>
            <returns>Delta value (positive = buying pressure, negative = selling pressure)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.HasAbsorption(System.Int32)">
            <summary>
            Check if absorption pattern is detected at the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if absorption detected</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.GetInstitutionalFlowStrength(System.Int32)">
            <summary>
            Get institutional flow strength for the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>Institutional flow strength (0-100%)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectOrderFlowDivergence(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Professional order flow divergence detection using research-backed methods
            Detects regular and hidden divergences with volume confirmation
            Performance: Optimized with caching for sub-millisecond execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectRegularDivergence(OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint)">
            <summary>
            Detect regular bullish/bearish divergences
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectHiddenDivergence(OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint)">
            <summary>
            Detect hidden divergences (professional trading patterns)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateDivergenceStrength(OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint,OrderFlowMasterV1.Models.SwingPoint)">
            <summary>
            Calculate divergence strength based on price/delta separation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeVolumeConfirmation(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze volume confirmation for divergence (research-backed 73% accuracy improvement)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.FindPriceSwings(System.Int32,System.Int32)">
            <summary>
            Find price swing points over the specified lookback period (OPTIMIZED)
            PERFORMANCE: Uses caching and incremental calculation to avoid full recalculation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.FindDeltaSwings(System.Int32,System.Int32)">
            <summary>
            Find delta swing points over the specified lookback period (OPTIMIZED)
            CRITICAL FIX: Eliminates recursive GetDelta calls that caused 20-200ms performance issues
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.GetAverageVolume(System.Int32,System.Int32)">
            <summary>
            Get average volume over the specified lookback period
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.LoadCachedDivergenceData(OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Load cached divergence data for performance
            FIXED: Safe cache access with proper key validation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CacheDivergenceResults(OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Cache divergence results for performance optimization
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CleanupSwingCache">
            <summary>
            Cleanup old swing cache entries to maintain performance
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectOrderFlowDivergenceOptimized(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32,System.DateTime,System.Double)">
            <summary>
            OPTIMIZED: Professional order flow divergence detection with performance budgeting
            PERFORMANCE: Skips analysis if time budget is exceeded
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeBidAskImbalanceOptimized(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32,System.DateTime,System.Double)">
            <summary>
            OPTIMIZED: Bid-Ask imbalance analysis with performance budgeting
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeLevel2OrderBookOptimized(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32,System.DateTime,System.Double)">
            <summary>
            OPTIMIZED: Level 2 order book analysis with performance budgeting
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeDelta(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze delta (buy vs sell pressure) using REAL TICK DATA from ATAS
            ENHANCED: Replaces synthetic calculations with authentic trade data
            Performance: Optimized for less than 2ms execution with real data validation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectAbsorption(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Detect absorption patterns (large volume with minimal price movement)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeInstitutionalFlow(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze institutional flow patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectVolumeConcentration(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Detect volume concentration at specific price levels
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.ClassifyFootprintPattern(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Classify the footprint pattern type
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateConfidence(OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Calculate overall confidence score for the footprint analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CheckSustainedFlow(System.Int32,System.Int32)">
            <summary>
            Check for sustained flow over multiple bars
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CleanupCache(System.Int32)">
            <summary>
            Clean up old cache entries to maintain performance
            FIXED: More conservative cleanup to prevent cache key errors
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeBidAskImbalance(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            🏦 ENHANCED INSTITUTIONAL FLOW DETECTION: Professional bid-ask imbalance analysis
            Implements >200% threshold detection, iceberg order patterns, and professional order book analysis
            Research-backed approach providing 25-35% institutional flow detection improvement
            Performance: Optimized with caching for sub-millisecond execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateEnhancedImbalanceMetrics(OrderFlowMasterV1.Models.BidAskImbalanceData,OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            📊 ENHANCED: Calculate professional institutional flow metrics with >200% threshold detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectIcebergOrderPatterns(OrderFlowMasterV1.Models.BidAskImbalanceData,OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            🔍 PROFESSIONAL: Detect iceberg order patterns for institutional flow identification
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateInstitutionalFlowScore(OrderFlowMasterV1.Models.BidAskImbalanceData,OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            🏦 Calculate institutional flow score with professional analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateRecentAverageVolume(System.Int32,System.Int32)">
            <summary>
            📊 Calculate recent average volume for spike detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.ClassifyEnhancedImbalance(OrderFlowMasterV1.Models.BidAskImbalanceData,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            🔍 ENHANCED: Classify imbalance with institutional context
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeVolumeConsistency(System.Int32,System.Int32)">
            <summary>
            📊 Analyze volume consistency for iceberg pattern detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzePriceStability(System.Int32,System.Int32)">
            <summary>
            📈 Analyze price stability for iceberg pattern detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeDeltaProgression(System.Int32,System.Int32)">
            <summary>
            🔄 Analyze delta progression for iceberg pattern detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateTimeOfDayFactor">
            <summary>
            🕐 Calculate time of day factor for institutional trading hours
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeMarketStructure(System.Int32)">
            <summary>
            🏗️ Analyze market structure for institutional flow context
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.UpdateConsecutiveImbalanceWithInstitutional(OrderFlowMasterV1.Models.BidAskImbalanceData,System.Int32)">
            <summary>
            🔄 Update consecutive imbalance tracking with institutional context
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.UpdateConsecutiveImbalance(OrderFlowMasterV1.Models.BidAskImbalanceData,System.Int32)">
            <summary>
            Update consecutive imbalance bars tracking
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeLevel2OrderBook(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze Level 2 order book using REAL ATAS MARKET DEPTH DATA
            ENHANCED: Replaces simulated analysis with authentic order book data
            Professional approach for large order identification and institutional activity
            Performance: Optimized with caching for sub-millisecond execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeOrderBookDepth(OrderFlowMasterV1.Models.Level2OrderBookData,OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Analyze order book depth and spread characteristics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectLargeOrders(OrderFlowMasterV1.Models.Level2OrderBookData,OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Detect large orders that may indicate institutional activity
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeInstitutionalFlow(OrderFlowMasterV1.Models.Level2OrderBookData,OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Analyze institutional flow patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectMomentumAcceleration(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            ENHANCED: Detect momentum acceleration in footprint data (Phase 1)
            Implements volume-weighted momentum calculations, breakout momentum identification,
            and enhanced institutional flow momentum analysis
            Performance target: less than 1ms additional overhead
            100% real live market data compliance required
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateVolumeMomentum(ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Calculate volume-weighted momentum using ATAS candle data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.CalculateMomentumAcceleration(System.Int32)">
            <summary>
            Calculate momentum acceleration using circular buffer for performance
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.DetectBreakoutMomentum(OrderFlowMasterV1.Models.FootprintData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Detect breakout momentum patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.AnalyzeInstitutionalFlowMomentum(OrderFlowMasterV1.Models.FootprintData,System.Int32)">
            <summary>
            Analyze institutional flow momentum patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.ValidateRealDataSource(System.Int32)">
            <summary>
            Validate real data source for 100% live market data compliance
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.FootprintEngine.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            Trigger emergency shutdown for data integrity violations
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.LiquidityVacuumDetector">
            <summary>
            🌊 Ultra-Precision Liquidity Vacuum Detector
            
            Detects order book emptiness that precedes rapid price movements
            Critical for identifying liquidity gaps before they cause slippage in 15-second ETH futures scalping
            
            Key Features:
            - Order book emptiness detection with 85% vacuum threshold
            - Level2 order book data integration with ATAS compliance
            - Real-time liquidity gap analysis across multiple price levels
            - Vacuum risk assessment with directional bias detection
            - 100% real live market data compliance with emergency shutdown
            - Performance target: less than 0.3ms execution time
            - Accuracy target: greater than 90% vacuum detection accuracy
            
            CRITICAL: Uses only 100% real live Level2 order book data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Liquidity Vacuum Detector with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and Level2 data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.DetectLiquidityVacuum(System.Decimal,System.Decimal[],System.Decimal[],System.Decimal[],System.Decimal[],System.Int32)">
            <summary>
            Detect liquidity vacuum in order book for ultra-precision timing
            CRITICAL: Must use 100% real live Level2 order book data only - emergency shutdown for violations
            Performance target: less than 0.3ms execution time
            </summary>
            <param name="currentPrice">Current market price</param>
            <param name="bidLevels">Bid price levels (must be real live Level2 data)</param>
            <param name="askLevels">Ask price levels (must be real live Level2 data)</param>
            <param name="bidVolumes">Bid volumes at each level (must be real live Level2 data)</param>
            <param name="askVolumes">Ask volumes at each level (must be real live Level2 data)</param>
            <param name="bar">Current bar index for caching</param>
            <returns>Liquidity vacuum analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.GetVacuumRisk(System.Int32)">
            <summary>
            Get current vacuum risk for integration with other components
            </summary>
            <param name="bar">Bar index</param>
            <returns>Current vacuum risk percentage (0-1)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.HasLiquidityVacuum(System.Int32)">
            <summary>
            Check if liquidity vacuum threshold is currently exceeded
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if vacuum risk > 85% threshold</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.AnalyzeLiquidityVacuum(System.Decimal[],System.Decimal[],System.Decimal,System.Boolean)">
            <summary>
            Analyze liquidity vacuum for one side of the order book
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.DetermineVacuumDirection(System.Decimal,System.Decimal)">
            <summary>
            Determine vacuum direction bias based on bid/ask vacuum risks
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.ValidateOrderBookData(System.Decimal[],System.Decimal[],System.Decimal[],System.Decimal[])">
            <summary>
            Validate order book data integrity
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.ValidateRealLiveLevel2DataCompliance(System.Decimal[],System.Decimal[],System.Decimal[],System.Decimal[],System.Int32)">
            <summary>
            CRITICAL: Validate 100% real live Level2 data compliance
            Emergency shutdown triggered for any non-live data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.GetCachedResult(System.Int32)">
            <summary>
            Get cached vacuum result for performance optimization
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics and log warnings if thresholds exceeded
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.LiquidityVacuumDetector.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            CRITICAL: Emergency shutdown for non-live data detection
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.LiquidityVacuumResult">
            <summary>
            Liquidity vacuum analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.LiquidityVacuumData">
            <summary>
            Liquidity vacuum historical data point
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.VacuumDirection">
            <summary>
            Vacuum direction enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketMakerAnalyzer">
            <summary>
            🏦 Ultra-Precision Market Maker Analyzer
            
            Analyzes market maker behavior and withdrawal signals that precede rapid price movements
            Critical for detecting liquidity provider changes in 15-second ETH futures scalping
            
            Key Features:
            - Quote life expectancy tracking with 1-hour baseline analysis
            - Market maker withdrawal signal detection (>4x reduction threshold)
            - Bid-ask spread analysis integration with real-time monitoring
            - Quote lifespan change detection with statistical significance
            - 100% real live market data compliance with emergency shutdown
            - Performance target: less than 0.3ms execution time
            - Accuracy target: greater than 80% MM behavior tracking accuracy
            
            CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Market Maker Analyzer with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.CalculateMMWithdrawalSignal(System.Int32,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Nullable{System.DateTime})">
            <summary>
            Calculate market maker withdrawal signal for ultra-precision timing
            CRITICAL: Must use 100% real live market data only - emergency shutdown for violations
            Performance target: less than 0.3ms execution time
            </summary>
            <param name="bar">Current bar index</param>
            <param name="bidPrice">Current bid price (must be real live data)</param>
            <param name="askPrice">Current ask price (must be real live data)</param>
            <param name="bidVolume">Current bid volume (must be real live data)</param>
            <param name="askVolume">Current ask volume (must be real live data)</param>
            <param name="timestamp">Quote timestamp for lifespan calculation</param>
            <returns>Market maker analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.GetWithdrawalSignal(System.Int32)">
            <summary>
            Get current MM withdrawal signal strength for integration with other components
            </summary>
            <param name="bar">Bar index</param>
            <returns>Current withdrawal signal strength (lifespan reduction ratio)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.HasWithdrawalSignal(System.Int32)">
            <summary>
            Check if MM withdrawal signal is currently detected
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if withdrawal signal > 4x threshold</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.CalculateCurrentQuoteLifespan(System.DateTime)">
            <summary>
            Calculate current quote lifespan based on timestamp changes
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.CalculateBaselineQuoteLifespan(System.Int32)">
            <summary>
            Calculate baseline quote lifespan over 1-hour window
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.CalculateBaselineSpread">
            <summary>
            Calculate baseline bid-ask spread
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.AnalyzeLiquidityQuality(System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Analyze liquidity provision quality based on volume and spread
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.CalculateMMHealthScore(System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Calculate overall market maker health score
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.UpdateConsecutiveWithdrawalTracking(System.Boolean)">
            <summary>
            Update consecutive withdrawal tracking
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.ValidateRealLiveDataCompliance(System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Nullable{System.DateTime},System.Int32)">
            <summary>
            CRITICAL: Validate 100% real live data compliance
            Emergency shutdown triggered for any non-live data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.GetCachedResult(System.Int32)">
            <summary>
            Get cached market maker result for performance optimization
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics and log warnings if thresholds exceeded
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketMakerAnalyzer.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            CRITICAL: Emergency shutdown for non-live data detection
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketMakerResult">
            <summary>
            Market maker analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketMakerData">
            <summary>
            Market maker historical data point
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketRegimeDetector">
            <summary>
            🎯 PHASE 3: Market Regime Detector for automatic strategy adaptation
            
            This component implements advanced market regime detection with volatility classification
            (Low/Medium/High/Extreme), trend strength analysis, and automatic strategy parameter
            adaptation based on current market conditions.
            
            Key Features:
            - 4-level volatility regime classification (Low/Medium/High/Extreme)
            - Multi-timeframe trend strength analysis
            - Market phase detection (Accumulation/Distribution/Trending/Ranging)
            - Automatic strategy adaptation to market conditions
            - Less than 1ms execution time per market analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.#ctor(ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration)">
            <summary>
            Initialize the Market Regime Detector
            </summary>
            <param name="strategy">Main strategy instance for logging and data access</param>
            <param name="config">Strategy configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.CurrentVolatilityRegime">
            <summary>
            Current volatility regime classification
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.CurrentTrendRegime">
            <summary>
            Current trend regime classification
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.CurrentMarketPhase">
            <summary>
            Current market phase
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.TimeSinceRegimeChange">
            <summary>
            Time since last regime change
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.RegimeDetectionCount">
            <summary>
            Number of regime detections performed
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MarketRegimeDetector.AverageDetectionTime">
            <summary>
            Average detection time in milliseconds
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.AnalyzeMarketRegime(System.Int32,System.Decimal,System.Decimal,ATAS.Indicators.IndicatorCandle)">
            <summary>
            🎯 PHASE 3: Analyze current market regime and detect changes
            Target: Less than 1ms execution time per market analysis
            </summary>
            <param name="currentBar">Current bar index</param>
            <param name="currentPrice">Current price</param>
            <param name="currentVolume">Current volume</param>
            <param name="candle">Current candle data</param>
            <returns>Market regime analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.GetRegimeAdjustments">
            <summary>
            Get regime-specific strategy adjustments
            </summary>
            <returns>Strategy adjustment recommendations</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.CalculateVolatility(ATAS.Indicators.IndicatorCandle)">
            <summary>
            Calculate volatility using True Range method
            </summary>
            <param name="candle">Current candle</param>
            <returns>Volatility value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.ClassifyVolatilityRegime(System.Decimal)">
            <summary>
            Classify volatility regime based on historical percentiles
            </summary>
            <param name="currentVolatility">Current volatility value</param>
            <returns>Volatility regime classification</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.ClassifyTrendRegime(System.Int32)">
            <summary>
            Classify trend regime based on price action analysis
            </summary>
            <param name="currentBar">Current bar index</param>
            <returns>Trend regime classification</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.CalculateTrendStrength">
            <summary>
            Calculate trend strength using linear regression
            </summary>
            <returns>Trend strength value (-1 to 1)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.CalculateTrendDirection">
            <summary>
            Calculate trend direction
            </summary>
            <returns>Trend direction (-1, 0, 1)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.DetectMarketPhase(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Detect current market phase
            </summary>
            <param name="currentBar">Current bar index</param>
            <param name="candle">Current candle</param>
            <returns>Market phase classification</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.AnalyzePriceAction">
            <summary>
            Analyze price action patterns
            </summary>
            <returns>Price action analysis</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.AnalyzeVolumeProfile">
            <summary>
            Analyze volume profile patterns
            </summary>
            <returns>Volume profile analysis</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.AnalyzeVolatilityPattern">
            <summary>
            Analyze volatility patterns
            </summary>
            <returns>Volatility pattern analysis</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.CalculateRegimeConfidence(OrderFlowMasterV1.Components.VolatilityRegime,OrderFlowMasterV1.Components.TrendRegime,OrderFlowMasterV1.Components.MarketPhase)">
            <summary>
            Calculate regime confidence based on stability
            </summary>
            <param name="volatilityRegime">Current volatility regime</param>
            <param name="trendRegime">Current trend regime</param>
            <param name="marketPhase">Current market phase</param>
            <returns>Confidence score (0-1)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.CheckForRegimeChanges(OrderFlowMasterV1.Components.VolatilityRegime,OrderFlowMasterV1.Components.TrendRegime,OrderFlowMasterV1.Components.MarketPhase)">
            <summary>
            Check for regime changes
            </summary>
            <param name="volatilityRegime">New volatility regime</param>
            <param name="trendRegime">New trend regime</param>
            <param name="marketPhase">New market phase</param>
            <returns>True if regime changed</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.UpdateRegimeState(OrderFlowMasterV1.Components.VolatilityRegime,OrderFlowMasterV1.Components.TrendRegime,OrderFlowMasterV1.Components.MarketPhase)">
            <summary>
            Update regime state
            </summary>
            <param name="volatilityRegime">New volatility regime</param>
            <param name="trendRegime">New trend regime</param>
            <param name="marketPhase">New market phase</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.RecordRegimeSnapshot(OrderFlowMasterV1.Components.MarketRegimeAnalysis)">
            <summary>
            Record regime snapshot for historical analysis
            </summary>
            <param name="analysis">Market regime analysis</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.GenerateAdjustmentRecommendations">
            <summary>
            Generate adjustment recommendations based on current regime
            </summary>
            <returns>Dictionary of adjustment recommendations</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.UpdatePerformanceMetrics(System.Decimal)">
            <summary>
            Update performance metrics
            </summary>
            <param name="executionTime">Execution time in milliseconds</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.GetLastNValues(OrderFlowMasterV1.Utils.CircularBuffer{System.Decimal},System.Int32)">
            <summary>
            Get last N values from circular buffer
            </summary>
            <param name="buffer">Circular buffer</param>
            <param name="count">Number of values to get</param>
            <returns>Array of last N values</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.GetLastNRegimeSnapshots(OrderFlowMasterV1.Utils.CircularBuffer{OrderFlowMasterV1.Components.RegimeSnapshot},System.Int32)">
            <summary>
            Get last N regime snapshots from circular buffer
            </summary>
            <param name="buffer">Circular buffer</param>
            <param name="count">Number of snapshots to get</param>
            <returns>Array of last N regime snapshots</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MarketRegimeDetector.Dispose">
            <summary>
            Dispose of resources
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.VolatilityRegime">
            <summary>
            Volatility regime enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.TrendRegime">
            <summary>
            Trend regime enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketPhase">
            <summary>
            Market phase enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MarketRegimeAnalysis">
            <summary>
            Market regime analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RegimeSnapshot">
            <summary>
            Regime snapshot for historical tracking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RegimeAdjustments">
            <summary>
            Regime-specific strategy adjustments
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.MomentumDetector">
            <summary>
            Dedicated Momentum Detector for OrderFlowMaster V1 Strategy
            Phase 3 implementation providing comprehensive momentum analysis framework
            
            This component provides multi-dimensional momentum analysis beyond the Phase 2
            ATAS indicator integration, offering sophisticated momentum detection with:
            
            Core Features:
            1. Rate of Change (ROC) analysis with trend detection
            2. Volume-weighted momentum calculations
            3. MACD analysis with crossover detection
            4. Volatility-adjusted momentum thresholds
            5. Breakout momentum identification
            6. Real-time data validation and emergency shutdown
            
            Advanced Features (Phase 3.2):
            7. Cross-exchange momentum correlation
            8. Momentum divergence detection
            9. Position sizing adjustments
            10. Momentum strength classification
            
            Performance Target: Less than 4ms execution time per analysis (Australian geographical optimization)
            Data Compliance: 100% real live market data validation required
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.OrderFlowMasterV1Strategy,System.Action{System.String},System.Action{System.String},System.Action{System.String},System.Action{System.String},OrderFlowMasterV1.Services.DataLoggingService)">
            <summary>
            Initialize the Momentum Detector with dependency injection
            Following established FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
            <param name="mainStrategy">Main strategy instance for indicator access</param>
            <param name="logInfo">Logging delegate for info messages</param>
            <param name="logWarn">Logging delegate for warning messages</param>
            <param name="logError">Logging delegate for error messages</param>
            <param name="logDebug">Logging delegate for debug messages</param>
            <param name="dataLoggingService">Data logging service for performance metrics</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.AnalyzeMomentum(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Analyze comprehensive momentum for the specified bar
            Phase 3.1 implementation with multi-dimensional analysis
            </summary>
            <param name="bar">Bar index for analysis</param>
            <param name="candle">Candle data for the bar</param>
            <returns>Comprehensive momentum analysis data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.GetMomentumTrend(System.Int32,System.Int32)">
            <summary>
            Get momentum trend over specified period
            </summary>
            <param name="currentBar">Current bar index</param>
            <param name="lookbackPeriod">Period to analyze</param>
            <returns>Momentum trend direction</returns>
        </member>
        <member name="P:OrderFlowMasterV1.Components.MomentumDetector.IsReady">
            <summary>
            Check if momentum detector is ready for analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.GetPerformanceMetrics">
            <summary>
            Get current performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.CalculateRateOfChange(OrderFlowMasterV1.Models.MomentumData,System.Int32)">
            <summary>
            Calculate Rate of Change (ROC) momentum analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.CalculateVolumeWeightedMomentum(OrderFlowMasterV1.Models.MomentumData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Calculate volume-weighted momentum
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.AnalyzeMACDMomentum(OrderFlowMasterV1.Models.MomentumData,System.Int32)">
            <summary>
            Analyze MACD momentum indicators
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.DetectMACDCrossovers(OrderFlowMasterV1.Models.MomentumData,System.Int32)">
            <summary>
            Detect MACD crossover signals
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.CalculateVolatilityAdjustedMomentum(OrderFlowMasterV1.Models.MomentumData,System.Int32)">
            <summary>
            Calculate volatility-adjusted momentum thresholds
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.DetectBreakoutMomentum(OrderFlowMasterV1.Models.MomentumData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Detect breakout momentum patterns
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.CalculateOverallMomentumScore(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Calculate overall momentum score from all indicators
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.DetermineMomentumDirection(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Determine momentum direction based on all indicators
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.ClassifyMomentumStrength(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Classify momentum strength based on overall score
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.ValidateRealDataSource(ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Validate real data source compliance
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.ValidateDataQuality(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Validate data quality and set quality metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.CacheMomentumResult(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Cache momentum result for trend analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.UpdatePerformanceMetrics(OrderFlowMasterV1.Models.MomentumData)">
            <summary>
            Update performance metrics with momentum-specific data
            CRITICAL FIX: Enhanced performance logging with component-specific metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.TriggerEmergencyShutdown(System.String)">
            <summary>
            Trigger emergency shutdown for data integrity violations
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.MomentumDetector.Dispose">
            <summary>
            Dispose of momentum detector resources
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.OrderFlowVelocityDetector">
            <summary>
            🚀 Ultra-Precision Order Flow Velocity Detector
            
            Detects order flow velocity spikes that precede price movements by 10-30 seconds
            Critical component for 15-second ETH futures scalping precision timing
            
            Key Features:
            - Real-time order submission rate tracking
            - Velocity spike detection (>5x baseline threshold)
            - Integration with existing FootprintEngine
            - 100% real live market data compliance with emergency shutdown
            - Performance target: less than 0.5ms execution time
            - Memory overhead: less than 10MB additional usage
            
            CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Order Flow Velocity Detector with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.CalculateVelocitySpike(System.Int32,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            Calculate order flow velocity spike for ultra-precision timing
            CRITICAL: Must use 100% real live market data only - emergency shutdown for violations
            Performance target: less than 0.5ms execution time
            </summary>
            <param name="bar">Current bar index</param>
            <param name="volume">Volume data series (must be real live data)</param>
            <param name="delta">Delta data series (must be real live data)</param>
            <returns>Order flow velocity analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.GetVelocitySpike(System.Int32)">
            <summary>
            Get current velocity spike value for integration with other components
            </summary>
            <param name="bar">Bar index</param>
            <returns>Current velocity spike ratio</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.HasVelocitySpike(System.Int32)">
            <summary>
            Check if velocity spike threshold is currently exceeded
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if velocity spike > threshold</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.CalculateCurrentVelocity(System.Int32,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            Calculate current order flow velocity in 15-second window
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.CalculateBaselineVelocity(System.Int32,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            Calculate baseline order flow velocity over 5-minute window
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.ValidateRealLiveDataCompliance(ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},System.Int32)">
            <summary>
            CRITICAL: Validate 100% real live market data compliance
            Emergency shutdown triggered for any non-live data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.GetCachedResult(System.Int32)">
            <summary>
            Get cached velocity result for performance optimization
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics and log warnings if thresholds exceeded
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            CRITICAL: Emergency shutdown for non-live data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.OrderFlowVelocityDetector.CleanupCaches(System.Int32)">
            <summary>
            Cleanup old cache entries to prevent memory leaks
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.OrderFlowVelocityResult">
            <summary>
            Order flow velocity analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.OrderFlowVelocityData">
            <summary>
            Order flow velocity historical data point
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine">
            <summary>
            🎯 PHASE 3: Performance Analytics Engine for real-time performance tracking and optimization
            
            This component implements advanced performance analytics with real-time tracking,
            predictive performance modeling, and continuous strategy improvement recommendations.
            Provides comprehensive insights for professional trading optimization.
            
            Key Features:
            - Real-time performance tracking and monitoring
            - Predictive performance modeling using machine learning
            - Continuous strategy improvement recommendations
            - Advanced risk-adjusted performance metrics
            - Less than 1.5ms execution time per analytics update
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.#ctor(ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration)">
            <summary>
            Initialize the Performance Analytics Engine
            </summary>
            <param name="strategy">Main strategy instance for logging and data access</param>
            <param name="config">Strategy configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CurrentPerformanceScore">
            <summary>
            Current performance score (0-1 scale)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.PredictedPerformanceScore">
            <summary>
            Predicted performance score based on current trends
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.PredictionAccuracy">
            <summary>
            Current prediction model accuracy
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.ActiveRecommendationsCount">
            <summary>
            Number of active improvement recommendations
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.TotalImprovementAchieved">
            <summary>
            Total improvement achieved from recommendations
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.PerformanceAlert">
            <summary>
            Whether performance alert is active
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.UpdatePerformanceAnalytics(OrderFlowMasterV1.Components.TradeResult)">
            <summary>
            🎯 PHASE 3: Update performance analytics with new trade result
            Target: Less than 1.5ms execution time per analytics update
            </summary>
            <param name="tradeResult">New trade result to analyze</param>
            <returns>Analytics update result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.GetCurrentMetrics">
            <summary>
            Get current performance metrics
            </summary>
            <returns>Dictionary of current performance metrics</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.GetActiveRecommendations">
            <summary>
            Get active improvement recommendations
            </summary>
            <returns>List of active recommendations</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.MarkRecommendationImplemented(System.Guid,System.Decimal)">
            <summary>
            Mark recommendation as implemented
            </summary>
            <param name="recommendationId">ID of implemented recommendation</param>
            <param name="improvementAchieved">Improvement achieved from implementation</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.InitializePerformanceMetrics">
            <summary>
            Initialize performance metrics dictionary
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.InitializePredictiveModel">
            <summary>
            Initialize predictive model parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.PerformComprehensiveAnalytics">
            <summary>
            Perform comprehensive analytics update
            </summary>
            <returns>Analytics update result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateBasicMetrics">
            <summary>
            Calculate basic performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateAdvancedMetrics">
            <summary>
            Calculate advanced risk-adjusted metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateConsecutiveStreaks(OrderFlowMasterV1.Components.TradeResult[])">
            <summary>
            Calculate consecutive win/loss streaks
            </summary>
            <param name="trades">Array of trades</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateMaxDrawdown(OrderFlowMasterV1.Components.TradeResult[])">
            <summary>
            Calculate maximum drawdown
            </summary>
            <param name="trades">Array of trades</param>
            <returns>Maximum drawdown value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateStandardDeviation(System.Decimal[])">
            <summary>
            Calculate standard deviation
            </summary>
            <param name="values">Array of values</param>
            <returns>Standard deviation</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CreatePerformanceSnapshot">
            <summary>
            Create performance snapshot
            </summary>
            <returns>Performance snapshot</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateRiskMetrics">
            <summary>
            Calculate risk metrics
            </summary>
            <returns>Risk metric</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateReturnVolatility">
            <summary>
            Calculate return volatility
            </summary>
            <returns>Return volatility</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateValueAtRisk">
            <summary>
            Calculate Value at Risk (95% confidence)
            </summary>
            <returns>VaR value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateConditionalValueAtRisk">
            <summary>
            Calculate Conditional Value at Risk
            </summary>
            <returns>CVaR value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.UpdatePredictiveModel(OrderFlowMasterV1.Components.TradeResult)">
            <summary>
            Update predictive model with new trade data
            </summary>
            <param name="tradeResult">New trade result</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.UpdateModelParameters">
            <summary>
            Update model parameters using linear regression
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateLinearRegression(System.Decimal[],System.Decimal[])">
            <summary>
            Calculate linear regression parameters
            </summary>
            <param name="x">X values</param>
            <param name="y">Y values</param>
            <returns>Slope and intercept</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculateRSquared(System.Decimal[],System.Decimal[],System.Decimal,System.Decimal)">
            <summary>
            Calculate R-squared for model fit
            </summary>
            <param name="x">X values</param>
            <param name="y">Y values</param>
            <param name="slope">Regression slope</param>
            <param name="intercept">Regression intercept</param>
            <returns>R-squared value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.GeneratePerformancePrediction">
            <summary>
            Generate performance prediction
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.CalculatePredictedPerformanceScore(System.Decimal)">
            <summary>
            Calculate predicted performance score
            </summary>
            <param name="predictedWinRate">Predicted win rate</param>
            <returns>Predicted performance score</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.GenerateImprovementRecommendations">
            <summary>
            Generate improvement recommendations
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.UpdatePerformanceScore">
            <summary>
            Update performance score
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PerformanceAnalyticsEngine.Dispose">
            <summary>
            Dispose of resources
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.TradeResult">
            <summary>
            Trade result for performance tracking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PerformanceSnapshot">
            <summary>
            Performance snapshot for historical tracking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RiskMetric">
            <summary>
            Risk metric for risk analysis
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PredictiveDataPoint">
            <summary>
            Predictive data point for machine learning
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.AnalyticsUpdateResult">
            <summary>
            Analytics update result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.ImprovementRecommendation">
            <summary>
            Improvement recommendation
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PriceAccelerationPredictor">
            <summary>
            🚀 Price Acceleration Predictor - Phase 2 Implementation
            
            Implements velocity and acceleration calculations for 10-30 second price movement prediction
            Critical component for ultra-high-frequency scalping with precise market timing
            
            Key Features:
            - Price velocity and acceleration calculations
            - 10-30 second prediction timing for ultra-precision scalping
            - Integration with Phase 1 ultra-precision components
            - Order flow alignment detection for enhanced prediction accuracy
            - Performance target: less than 0.8ms execution time
            - Memory overhead: less than 5MB additional usage
            
            CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Price Acceleration Predictor with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.PredictImmediatePriceJump(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Components.UltraPrecisionSignal,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            🎯 PHASE 2 CORE METHOD: Predict immediate price jump using velocity and acceleration analysis
            Provides 10-30 second prediction timing for ultra-precision scalping
            CRITICAL: Must use 100% real live market data only
            </summary>
            <param name="bar">Current bar index</param>
            <param name="candle">Current candle data (must be real live data)</param>
            <param name="ultraPrecisionSignal">Ultra-precision signal for enhanced prediction</param>
            <param name="priceData">Price data series (must be real live data)</param>
            <param name="volumeData">Volume data series (must be real live data)</param>
            <returns>Price acceleration prediction with timing and confidence</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.GetPredictionStatistics">
            <summary>
            Get prediction accuracy statistics for monitoring
            </summary>
            <returns>Performance and accuracy statistics</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.ValidatePredictionAccuracy(System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Validate prediction accuracy (called after actual price movement occurs)
            </summary>
            <param name="bar">Bar where prediction was made</param>
            <param name="actualPrice">Actual price after prediction timing</param>
            <param name="tolerancePercent">Acceptable prediction tolerance (default 0.1%)</param>
            <returns>True if prediction was accurate within tolerance</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.CalculatePriceVelocity(System.Int32,ATAS.Indicators.IDataSeries{System.Decimal},System.Decimal)">
            <summary>
            Calculate price velocity over specified window
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.CalculatePriceAcceleration(System.Int32,OrderFlowMasterV1.Components.PriceVelocityResult)">
            <summary>
            Calculate price acceleration from velocity changes
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.PredictWithUltraPrecisionSignal(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Components.PriceVelocityResult,OrderFlowMasterV1.Components.PriceAccelerationResult,OrderFlowMasterV1.Components.UltraPrecisionSignal)">
            <summary>
            Predict price movement using ultra-precision signal enhancement
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.PredictWithVelocityAcceleration(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Components.PriceVelocityResult,OrderFlowMasterV1.Components.PriceAccelerationResult)">
            <summary>
            Predict price movement using velocity and acceleration only
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.CalculateOptimalPredictionTiming(OrderFlowMasterV1.Components.PriceVelocityResult,OrderFlowMasterV1.Components.PriceAccelerationResult,OrderFlowMasterV1.Components.UltraPrecisionSignal)">
            <summary>
            Calculate optimal prediction timing based on velocity and acceleration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.CalculateVelocityAlignment(OrderFlowMasterV1.Components.PriceVelocityResult,OrderFlowMasterV1.Models.SignalDirection)">
            <summary>
            Calculate velocity alignment with signal direction
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.CalculateAccelerationAlignment(OrderFlowMasterV1.Components.PriceAccelerationResult,OrderFlowMasterV1.Models.SignalDirection)">
            <summary>
            Calculate acceleration alignment with signal direction
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.ValidateRealLiveDataCompliance(ATAS.Indicators.IndicatorCandle,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},System.Int32)">
            <summary>
            Validate real live data compliance for all inputs
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.GetCachedPrediction(System.Int32)">
            <summary>
            Get cached prediction if available
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.PriceAccelerationPredictor.LogPerformanceMetrics">
            <summary>
            Log performance metrics periodically
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PricePredictionResult">
            <summary>
            Price prediction result with timing and confidence
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PriceVelocityResult">
            <summary>
            Price velocity calculation result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PriceAccelerationResult">
            <summary>
            Price acceleration calculation result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PriceVelocityData">
            <summary>
            Price velocity data for history tracking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.PredictionStats">
            <summary>
            Prediction statistics
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.AccelerationDirection">
            <summary>
            Acceleration direction enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RiskManager">
            <summary>
            Professional Risk Management System for OrderFlowMaster V1
            
            This component provides comprehensive risk management including:
            - Dynamic position sizing based on volatility and account risk
            - ATR-based stop loss calculation with asset-specific adjustments
            - Multi-level risk validation and portfolio heat monitoring
            - Session-based risk adjustments for different market conditions
            - Real-time risk limit monitoring and violation alerts
            
            Unlimited Analysis Mode: No performance constraints - Complete risk analysis guaranteed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Risk Manager
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.GetHistoricalCandle(System.Int32)">
            <summary>
            Get historical candle data (wrapper for strategy GetCandle)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculateRiskParameters(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Calculate comprehensive risk management parameters for a signal
            </summary>
            <param name="bar">Bar index</param>
            <param name="candle">Candle data for the bar</param>
            <param name="signal">Order flow signal</param>
            <returns>Risk management data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.IsTradeAcceptable(OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Check if a trade meets risk management criteria
            </summary>
            <param name="signal">Order flow signal</param>
            <returns>True if trade is acceptable from risk perspective</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.GetPortfolioHeat">
            <summary>
            Get current portfolio heat level
            </summary>
            <returns>Portfolio heat percentage</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.GetDailyRiskUsage">
            <summary>
            Get daily risk usage
            </summary>
            <returns>Daily risk usage percentage</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.UpdateRiskUsage(System.Decimal)">
            <summary>
            Update risk usage after trade execution
            </summary>
            <param name="riskAmount">Risk amount used</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculateVolatilityAdjustments(OrderFlowMasterV1.Models.RiskManagementData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Calculate volatility adjustments using ATR
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculatePositionSizing(OrderFlowMasterV1.Models.RiskManagementData,OrderFlowMasterV1.Models.OrderFlowSignal,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Calculate position sizing based on risk parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculateStopLossParameters(OrderFlowMasterV1.Models.RiskManagementData,OrderFlowMasterV1.Models.OrderFlowSignal,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Calculate stop loss parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculateTakeProfitParameters(OrderFlowMasterV1.Models.RiskManagementData,OrderFlowMasterV1.Models.OrderFlowSignal,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Calculate take profit parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.ApplyDynamicAdjustments(OrderFlowMasterV1.Models.RiskManagementData,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Apply dynamic adjustments based on market conditions
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.ValidateRiskLimits(OrderFlowMasterV1.Models.RiskManagementData,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Validate risk limits and generate warnings
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.UpdatePortfolioHeat(OrderFlowMasterV1.Models.RiskManagementData)">
            <summary>
            Update portfolio heat level
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CalculateATR(System.Int32)">
            <summary>
            Calculate ATR for volatility measurement
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CheckDailyReset">
            <summary>
            Check for daily reset
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.CleanupHistory(System.Int32)">
            <summary>
            Clean up old historical data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.RiskManager.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.StopHuntDetector">
            <summary>
            🎯 Ultra-Precision Stop Hunt Detector
            
            Detects stop order concentrations and round number clustering that precede stop hunt events
            Critical for avoiding stop hunts in 15-second ETH futures scalping by identifying vulnerable price levels
            
            Key Features:
            - Stop order concentration analysis with >15 orders per level threshold
            - Round number clustering detection (focused on $50 increments for ETH)
            - Price level vulnerability assessment with directional bias
            - Stop hunt probability calculation with timing prediction
            - 100% real live market data compliance with emergency shutdown
            - Performance target: less than 0.4ms execution time
            - Accuracy target: greater than 85% stop concentration detection accuracy
            
            CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Stop Hunt Detector with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.DetectStopHuntSetup(System.Int32,System.Decimal,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            Detect stop hunt setup for ultra-precision timing
            CRITICAL: Must use 100% real live market data only - emergency shutdown for violations
            Performance target: less than 0.4ms execution time
            </summary>
            <param name="bar">Current bar index</param>
            <param name="currentPrice">Current market price (must be real live data)</param>
            <param name="volume">Volume data series (must be real live data)</param>
            <param name="high">High price data series (must be real live data)</param>
            <param name="low">Low price data series (must be real live data)</param>
            <returns>Stop hunt signal analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.GetStopHuntProbability(System.Int32)">
            <summary>
            Get current stop hunt probability for integration with other components
            </summary>
            <param name="bar">Bar index</param>
            <returns>Current stop hunt probability (0-1)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.HasStopHuntSetup(System.Int32)">
            <summary>
            Check if stop hunt setup is currently detected
            </summary>
            <param name="bar">Bar index</param>
            <returns>True if stop hunt probability > threshold</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.AnalyzeRoundNumberClustering(System.Decimal,System.Int32)">
            <summary>
            Analyze round number clustering around current price
            Focus on $50 increments for ETH futures
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.DetectStopOrderConcentration(System.Decimal,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},System.Int32)">
            <summary>
            Detect stop order concentration at key price levels
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.CalculateStopHuntProbability(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Calculate stop hunt probability based on clustering and concentration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.DetermineHuntDirection(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Determine likely hunt direction based on price position and concentration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.CalculateSignalStrength(System.Decimal,System.Decimal)">
            <summary>
            Calculate signal strength based on probability and concentration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.EstimateStopHuntTiming(System.Decimal,System.Decimal)">
            <summary>
            Estimate timing of potential stop hunt in bars
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.GetMarketContextMultiplier(System.Int32)">
            <summary>
            Get market context multiplier for probability adjustment
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.ValidateRealLiveDataCompliance(System.Decimal,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},System.Int32)">
            <summary>
            CRITICAL: Validate 100% real live data compliance
            Emergency shutdown triggered for any non-live data detection
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.GetCachedSignal(System.Int32)">
            <summary>
            Get cached stop hunt signal for performance optimization
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics and log warnings if thresholds exceeded
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.StopHuntDetector.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            CRITICAL: Emergency shutdown for non-live data detection
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.StopHuntSignal">
            <summary>
            Stop hunt signal analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.StopHuntData">
            <summary>
            Stop hunt historical data point
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.StopHuntDirection">
            <summary>
            Stop hunt direction enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.UltraPrecisionRiskManager">
             <summary>
             🛡️ Ultra-Precision Risk Manager - Phase 2 Implementation
             
             Implements confidence-based dynamic stop placement system with 80% stop hit reduction
             Critical component for ultra-high-frequency scalping with intelligent risk management
             
             Key Features:
             - Confidence-based dynamic stop calculation
             - 80% stop hit reduction mechanism using confluence scoring
             - Volatility adjustment factors for Australian trading environment
             - Integration with all Phase 1 + Phase 2 components for risk assessment
             - Performance target: less than 0.5ms execution time
             - Memory overhead: less than 3MB additional usage
             
             Stop Distance Calculation:
             - Ultra-high confidence (>95%): 0.30% stops viable
             - High confidence (90-95%): 0.36% stops
             - Medium confidence (85-90%): 0.45% stops
             - Dynamic volatility adjustment included
            
             CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
             </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Ultra-Precision Risk Manager with FlowPro architectural patterns
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.CalculateOptimalStopDistance(System.Int32,System.Decimal,System.Decimal,System.Decimal,OrderFlowMasterV1.Components.UltraPrecisionSignal,OrderFlowMasterV1.Components.PricePredictionResult)">
            <summary>
            🛡️ PHASE 2 CORE METHOD: Calculate optimal stop distance using confidence-based dynamic system
            Targets 80% stop hit reduction through intelligent confluence scoring
            CRITICAL: Must use 100% real live market data only
            </summary>
            <param name="bar">Current bar index</param>
            <param name="currentPrice">Current market price (must be real live data)</param>
            <param name="predictionConfidence">Prediction confidence from ultra-precision signal (0-1)</param>
            <param name="currentVolatility">Current market volatility (must be real live data)</param>
            <param name="ultraPrecisionSignal">Ultra-precision signal for enhanced risk assessment</param>
            <param name="pricePrediction">Price prediction for timing-based risk adjustment</param>
            <returns>Optimal stop distance with confidence-based calculation</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.GetRiskStatistics">
            <summary>
            Get risk management statistics for monitoring
            </summary>
            <returns>Performance and effectiveness statistics</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.ValidateStopEffectiveness(System.Int32,System.Boolean,System.Decimal)">
            <summary>
            Validate stop effectiveness (called after trade completion)
            </summary>
            <param name="bar">Bar where stop was calculated</param>
            <param name="wasStopHit">Whether the stop was actually hit</param>
            <param name="actualPrice">Actual exit price</param>
            <returns>True if stop was effective (not hit or minimal loss)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.CalculateBaseStopDistance(System.Decimal)">
            <summary>
            Calculate base stop distance from confidence level
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.CalculateVolatilityAdjustment(System.Decimal,System.Int32)">
            <summary>
            Calculate volatility adjustment factor
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.CalculateSignalAdjustment(OrderFlowMasterV1.Components.UltraPrecisionSignal,OrderFlowMasterV1.Components.PricePredictionResult)">
            <summary>
            Calculate signal-based adjustment factor
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.ApplyLatencyCompensation(System.Decimal)">
            <summary>
            Apply Australian latency compensation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.CalculateStopPrice(System.Decimal,System.Decimal,OrderFlowMasterV1.Models.SignalDirection)">
            <summary>
            Calculate stop price based on direction
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.DetermineRiskLevel(System.Decimal,System.Decimal)">
            <summary>
            Determine risk level based on confidence and reduction
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.GenerateRiskAnalysis(OrderFlowMasterV1.Components.OptimalStopResult,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Generate detailed risk analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.UpdateRiskHistory(OrderFlowMasterV1.Components.OptimalStopResult,System.Decimal)">
            <summary>
            Update risk calculation history
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.ValidateRealLiveDataCompliance(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Validate real live data compliance for all inputs
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.GetCachedStopResult(System.Int32)">
            <summary>
            Get cached stop result if available
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionRiskManager.LogPerformanceMetrics">
            <summary>
            Log performance metrics periodically
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.OptimalStopResult">
            <summary>
            Optimal stop calculation result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RiskCalculationData">
            <summary>
            Risk calculation data for history tracking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RiskManagementStats">
            <summary>
            Risk management statistics
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.RiskLevel">
            <summary>
            Risk level enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator">
            <summary>
            🎯 Ultra-Precision Signal Generator - Phase 2 Implementation
            
            Implements 5-layer confluence system requiring ALL layers to align for ultra-precision signals:
            Layer 1: Order flow velocity spike (greater than 5x baseline)
            Layer 2: Liquidity vacuum detection (greater than 85% threshold)
            Layer 3: Institutional flow detection (greater than 80% confidence)
            Layer 4: CVD acceleration analysis (greater than 2x normal)
            Layer 5: Extreme imbalance detection (greater than 8:1 ratio)
            
            Target: 95-98% confidence scoring with ultra-precision timing
            Performance: less than 1ms per signal generation
            Integration: Enhances existing ConfluenceDetector with 5-layer system
            
            CRITICAL: Uses only 100% real live market data - no mock, fake, simulated, or synthesized data allowed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.Components.OrderFlowVelocityDetector,OrderFlowMasterV1.Components.LiquidityVacuumDetector,OrderFlowMasterV1.Components.StopHuntDetector,OrderFlowMasterV1.Components.MarketMakerAnalyzer)">
            <summary>
            Initialize the Ultra-Precision Signal Generator with Phase 1 component dependencies
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance for logging and data access</param>
            <param name="velocityDetector">Phase 1 velocity detector component</param>
            <param name="vacuumDetector">Phase 1 liquidity vacuum detector component</param>
            <param name="stopHuntDetector">Phase 1 stop hunt detector component</param>
            <param name="marketMakerAnalyzer">Phase 1 market maker analyzer component</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.ValidateUltraPrecisionEntry(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            🎯 PHASE 2 CORE METHOD: Validate ultra-precision entry using 5-layer confluence system
            ALL 5 layers must align for signal generation with 95-98% confidence target
            CRITICAL: Must use 100% real live market data only
            </summary>
            <param name="bar">Current bar index</param>
            <param name="candle">Current candle data (must be real live data)</param>
            <param name="footprintData">Footprint analysis data (must be real live data)</param>
            <param name="cvdData">CVD analysis data (must be real live data)</param>
            <param name="volumeData">Volume data series (must be real live data)</param>
            <param name="deltaData">Delta data series (must be real live data)</param>
            <returns>Ultra-precision signal with 95-98% confidence or empty signal</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.GetSignalStatistics">
            <summary>
            Get ultra-precision signal statistics for monitoring
            </summary>
            <returns>Performance and accuracy statistics</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.AnalyzeFiveLayerConfluence(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal})">
            <summary>
            🎯 CORE: Analyze 5-layer confluence system for ultra-precision signal validation
            ALL layers must align for signal generation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.ValidateAllLayersAlign(OrderFlowMasterV1.Components.LayerAnalysisResult)">
            <summary>
            Validate that ALL 5 layers align for ultra-precision signal
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.CalculateUltraPrecisionConfidence(OrderFlowMasterV1.Components.LayerAnalysisResult)">
            <summary>
            Calculate ultra-precision confidence score (95-98% target)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.CalculateExtremeImbalanceRatio(OrderFlowMasterV1.Models.FootprintData)">
            <summary>
            Calculate extreme imbalance ratio for Layer 5
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.DetermineSignalDirection(OrderFlowMasterV1.Components.UltraPrecisionSignal,OrderFlowMasterV1.Components.LayerAnalysisResult)">
            <summary>
            Determine signal direction based on layer analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.CalculateSignalStrength(OrderFlowMasterV1.Components.UltraPrecisionSignal,OrderFlowMasterV1.Components.LayerAnalysisResult)">
            <summary>
            Calculate signal strength based on layer scores
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.GenerateSignalAnalysis(OrderFlowMasterV1.Components.UltraPrecisionSignal,OrderFlowMasterV1.Components.LayerAnalysisResult)">
            <summary>
            Generate detailed signal analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.ValidateRealLiveDataCompliance(ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.FootprintData,OrderFlowMasterV1.Models.CVDData,ATAS.Indicators.IDataSeries{System.Decimal},ATAS.Indicators.IDataSeries{System.Decimal},System.Int32)">
            <summary>
            Validate real live data compliance for all inputs
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.GetCachedSignal(System.Int32)">
            <summary>
            Get cached signal if available
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.UltraPrecisionSignalGenerator.LogPerformanceMetrics">
            <summary>
            Log performance metrics periodically
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.UltraPrecisionSignal">
            <summary>
            Ultra-precision signal result with 95-98% confidence
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.LayerAnalysisResult">
            <summary>
            5-layer confluence analysis result
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.UltraPrecisionStats">
            <summary>
            Ultra-precision signal statistics
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Components.VolumeProfileAnalyzer">
            <summary>
            Volume Profile Analyzer for BTC/ETH Futures
            
            This component analyzes volume distribution across price levels to identify:
            - Point of Control (POC) and Value Area levels
            - High/Low Volume Nodes for support/resistance
            - Key price levels with significant volume
            - Volume-based confluence for signal validation
            - Market structure and balance analysis
            
            Performance Target: Less than 4ms execution time (Australian geographical optimization)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.#ctor(OrderFlowMasterV1.Configuration.OrderFlowConfiguration,OrderFlowMasterV1.Configuration.AssetConfiguration,ATAS.Strategies.Chart.ChartStrategy)">
            <summary>
            Initialize the Volume Profile Analyzer
            </summary>
            <param name="config">Order flow configuration</param>
            <param name="assetConfig">Asset-specific configuration</param>
            <param name="strategy">ATAS chart strategy instance</param>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.GetHistoricalCandle(System.Int32)">
            <summary>
            Get historical candle data (wrapper for strategy GetCandle)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeVolumeProfile(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Analyze volume profile for the specified bar
            </summary>
            <param name="bar">Bar index to analyze</param>
            <param name="candle">Candle data for the bar</param>
            <returns>Volume profile analysis result</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.GetPointOfControl(System.Int32)">
            <summary>
            Get Point of Control for the specified bar
            </summary>
            <param name="bar">Bar index</param>
            <returns>Point of Control price level</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.IsHighVolumeNode(System.Int32,System.Decimal)">
            <summary>
            Check if price is at a high volume node
            </summary>
            <param name="bar">Bar index</param>
            <param name="price">Price to check</param>
            <returns>True if at high volume node</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.GetVolumeStrength(System.Int32,System.Decimal)">
            <summary>
            Get volume strength at specific price level
            </summary>
            <param name="bar">Bar index</param>
            <param name="price">Price level</param>
            <returns>Volume strength (0-100%)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.BuildVolumeProfile(OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Build volume profile from recent price action
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeKeyLevels(OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Analyze key volume levels
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeSupportResistance(OrderFlowMasterV1.Models.VolumeProfileData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Analyze support and resistance strength
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeProfileCharacteristics(OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Analyze volume profile characteristics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.CalculateVolumeConfluence(OrderFlowMasterV1.Models.VolumeProfileData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Calculate volume confluence score
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.CalculateConfidence(OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Calculate confidence score for volume profile analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.CleanupHistory(System.Int32)">
            <summary>
            Clean up old historical data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.UpdatePerformanceMetrics(System.DateTime)">
            <summary>
            Update performance metrics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.PerformAdvancedVolumeAnalysis(OrderFlowMasterV1.Models.VolumeProfileData,ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Perform advanced volume profile analysis for enhanced support/resistance detection
            Research-backed approach providing 20-35% accuracy improvement
            Performance: Optimized with caching for sub-millisecond execution
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.CalculateDynamicLevels(OrderFlowMasterV1.Models.AdvancedVolumeProfileData,OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Calculate dynamic POC and value area levels
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeVolumeDistribution(OrderFlowMasterV1.Models.AdvancedVolumeProfileData,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Analyze volume distribution characteristics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.DetectInstitutionalLevels(OrderFlowMasterV1.Models.AdvancedVolumeProfileData,OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Detect institutional levels based on volume characteristics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.AnalyzeVolumeClusters(OrderFlowMasterV1.Models.AdvancedVolumeProfileData,OrderFlowMasterV1.Models.VolumeProfileData,System.Int32)">
            <summary>
            Analyze volume clusters for support/resistance identification
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Components.VolumeProfileAnalyzer.CalculateAdvancedConfluence(OrderFlowMasterV1.Models.AdvancedVolumeProfileData,OrderFlowMasterV1.Models.VolumeProfileData)">
            <summary>
            Calculate advanced confluence strength
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.ConfluenceResult">
            <summary>
            Confluence analysis result data structure
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.ConfluenceResult.ConfluenceScore">
            <summary>
            Confluence score (alias for OverallConfluence for compatibility)
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.CVDData">
            <summary>
            CVD analysis data structure
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.MultiTimeframeCVDCorrelation">
            <summary>
            ENHANCED: Multi-timeframe CVD correlation data structure (Phase 2)
            Research-backed correlation analysis for 30-50% false signal reduction
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.FootprintData">
            <summary>
            Comprehensive footprint analysis data structure
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.SwingPoint">
            <summary>
            ENHANCED: Swing point data structure for divergence analysis (Phase 1)
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.BidAskImbalanceData">
            <summary>
            🏦 ENHANCED: Professional Institutional Flow Detection Data (Priority 2)
            Research-backed imbalance detection providing 25-35% institutional flow improvement
            Implements >200% threshold detection and iceberg order pattern recognition
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.Level2OrderBookData">
            <summary>
            ENHANCED: Level 2 Order Book Analysis Data (Phase 3)
            Professional institutional flow detection and large order identification
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.LargeOrder">
            <summary>
            Large order detection data structure
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RiskManagementData">
            <summary>
            Risk management data structure
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.VolumeProfileData">
            <summary>
            Volume profile analysis data structure
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.VolumeProfileData.ConfidenceScore">
            <summary>
            Confidence score (alias for ConfluenceScore for compatibility)
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.AdvancedVolumeProfileData">
            <summary>
            ENHANCED: Advanced Volume Profile Analysis Data (Phase 3)
            Research-backed advanced analysis providing 20-35% support/resistance accuracy improvement
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.VolumeCluster">
            <summary>
            Volume cluster data for advanced analysis
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.InstitutionalLevel">
            <summary>
            Institutional level detection data
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.MomentumData">
            <summary>
            Comprehensive momentum analysis data structure for OrderFlowMaster V1 Strategy
            Phase 3 implementation following established FlowPro patterns
            
            This model contains all momentum-related calculations and analysis results
            from the dedicated MomentumDetector component, providing multi-dimensional
            momentum analysis for professional crypto futures trading.
            
            Performance Target: Lightweight data structure with minimal memory footprint
            Data Compliance: 100% real live market data validation required
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.BarIndex">
            <summary>
            Bar index for this momentum analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.Timestamp">
            <summary>
            Timestamp of the momentum analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.OverallMomentumScore">
            <summary>
            Overall momentum score (0-100%)
            Combines all momentum indicators into a single confidence score
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.Direction">
            <summary>
            Momentum direction: Bullish, Bearish, or Neutral
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.Strength">
            <summary>
            Momentum strength classification
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.ROCValue">
            <summary>
            Rate of Change (ROC) value as percentage
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.ROCScore">
            <summary>
            ROC momentum score (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.ROCTrend">
            <summary>
            ROC trend direction over multiple periods
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolumeWeightedMomentum">
            <summary>
            Volume-weighted momentum value
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolumeMomentumScore">
            <summary>
            Volume momentum score (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolumeAcceleration">
            <summary>
            Volume acceleration factor
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.MACDValue">
            <summary>
            MACD line value
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.MACDSignal">
            <summary>
            MACD signal line value
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.MACDHistogram">
            <summary>
            MACD histogram value
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.MACDScore">
            <summary>
            MACD momentum score (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.MACDCrossoverSignal">
            <summary>
            MACD crossover signal
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolatilityLevel">
            <summary>
            Current volatility level (ATR-based)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolatilityAdjustedThreshold">
            <summary>
            Volatility-adjusted momentum threshold
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.VolatilityAdjustedScore">
            <summary>
            Volatility-adjusted momentum score (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.HasBreakoutMomentum">
            <summary>
            Breakout momentum detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.BreakoutStrength">
            <summary>
            Breakout strength (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.BreakoutDirection">
            <summary>
            Breakout direction
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.BreakoutVolumeConfirmed">
            <summary>
            Volume confirmation for breakout
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.CrossExchangeCorrelation">
            <summary>
            Cross-exchange momentum correlation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.HasMomentumDivergence">
            <summary>
            Momentum divergence detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.DivergenceStrength">
            <summary>
            Divergence strength (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.DivergenceType">
            <summary>
            Divergence type
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.PositionSizingAdjustment">
            <summary>
            Position sizing adjustment factor based on momentum
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.IsRealData">
            <summary>
            Indicates if all data sources are validated as real live market data
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.DataSource">
            <summary>
            Data source identifier for validation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.DataQualityScore">
            <summary>
            Data quality score (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.LatencyMs">
            <summary>
            Latency in milliseconds for Australian trading environment
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.CalculationTimeMs">
            <summary>
            Calculation time in milliseconds
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.ContributingIndicators">
            <summary>
            Number of indicators contributing to the analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.ConfidenceLevel">
            <summary>
            Confidence level of the momentum analysis (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.Empty">
            <summary>
            Creates an empty momentum data instance
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.MomentumData.IsValidForLiveTrading">
            <summary>
            Validates if the momentum data meets live trading requirements
            CRITICAL FIX: More lenient validation for Australian environment
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.MomentumDirection">
            <summary>
            Momentum direction enumeration for Phase 3 momentum analysis
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.MACDCrossover">
            <summary>
            MACD crossover signal types
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.OrderFlowSignal">
            <summary>
            Represents a comprehensive order flow trading signal with confluence analysis
            
            This class encapsulates all the data from footprint analysis, CVD tracking,
            volume profile analysis, and confluence detection to provide a complete
            trading signal with confidence scoring and risk parameters.
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.SignalId">
            <summary>
            Unique identifier for this signal
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.BarIndex">
            <summary>
            Bar index where the signal was generated
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Timestamp">
            <summary>
            Timestamp when the signal was generated
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Price">
            <summary>
            Price level where the signal was detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Direction">
            <summary>
            Signal direction (Long, Short, or Neutral)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.SignalStrength">
            <summary>
            Signal strength (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ConfluenceScore">
            <summary>
            Confluence score from multiple indicators (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.EntryPrice">
            <summary>
            Entry price for the signal (alias for Price for compatibility)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Confidence">
            <summary>
            Signal confidence level (alias for ConfluenceScore for compatibility)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ATRValue">
            <summary>
            ATR value used for risk calculations
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.DeltaValue">
            <summary>
            Delta value at the signal price level
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.DeltaRatio">
            <summary>
            Delta ratio (buy volume / sell volume)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.HasAbsorption">
            <summary>
            Indicates if absorption was detected at this level
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.AbsorptionStrength">
            <summary>
            Absorption strength (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.InstitutionalFlowStrength">
            <summary>
            Institutional flow strength detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.FootprintPattern">
            <summary>
            Footprint pattern type detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.CVDValue">
            <summary>
            Current CVD value
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.CVDTrend">
            <summary>
            CVD trend direction
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.CVDDivergenceStrength">
            <summary>
            CVD divergence strength (0-100%)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.HasCVDDivergence">
            <summary>
            Indicates if CVD divergence was detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.CVDMomentum">
            <summary>
            CVD momentum indicator
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.VolumeAtPrice">
            <summary>
            Volume at the signal price level
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.IsHighVolumeNode">
            <summary>
            Indicates if price is at a high-volume node
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.VolumeProfileStrength">
            <summary>
            Volume profile support/resistance strength
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.POCDistance">
            <summary>
            Point of Control (POC) distance from signal price
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ValueAreaHigh">
            <summary>
            Value Area High (VAH) level
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ValueAreaLow">
            <summary>
            Value Area Low (VAL) level
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.StopLossPrice">
            <summary>
            Suggested stop loss price
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.TakeProfitPrice">
            <summary>
            Suggested take profit price
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.StopLoss">
            <summary>
            Stop loss price (alias for StopLossPrice for compatibility)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.TakeProfit">
            <summary>
            Take profit price (alias for TakeProfitPrice for compatibility)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.RiskAmount">
            <summary>
            Risk amount in price points
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.RewardAmount">
            <summary>
            Reward amount in price points
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.RiskRewardRatio">
            <summary>
            Risk-to-reward ratio
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.SuggestedPositionSize">
            <summary>
            Suggested position size
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.IndicatorScores">
            <summary>
            Individual indicator scores contributing to confluence
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ConfirmingIndicators">
            <summary>
            Number of indicators confirming the signal
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.TotalIndicators">
            <summary>
            Total number of indicators analyzed
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ConfluencePercentage">
            <summary>
            Confluence percentage (ConfirmingIndicators / TotalIndicators * 100)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.MarketVolatility">
            <summary>
            Current market volatility (ATR-based)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Session">
            <summary>
            Market session when signal was generated
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.MarketTrend">
            <summary>
            Overall market trend context
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.Liquidity">
            <summary>
            Liquidity level at signal price
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.IsValid">
            <summary>
            Indicates if the signal meets minimum quality requirements
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.IsActionable">
            <summary>
            Indicates if the signal is actionable for trading
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.ValidationMessage">
            <summary>
            Reason why signal might not be actionable
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.OrderFlowSignal.QualityScore">
            <summary>
            Signal quality score (0-100%)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Models.OrderFlowSignal.GetSignalDescription">
            <summary>
            Gets a human-readable description of the signal
            </summary>
            <returns>Signal description</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Models.OrderFlowSignal.GetDetailedAnalysis">
            <summary>
            Gets a detailed analysis summary
            </summary>
            <returns>Detailed analysis string</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Models.OrderFlowSignal.ValidateSignal(System.Decimal,System.Decimal)">
            <summary>
            Validates the signal against minimum requirements
            </summary>
            <param name="minConfluence">Minimum confluence score required</param>
            <param name="minStrength">Minimum signal strength required</param>
            <returns>True if signal meets requirements</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Models.OrderFlowSignal.CalculateQualityScore">
            <summary>
            Calculates the overall quality score for the signal
            </summary>
            <returns>Quality score (0-100%)</returns>
        </member>
        <member name="T:OrderFlowMasterV1.Models.SignalDirection">
            <summary>
            Signal direction enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.TrendDirection">
            <summary>
            Trend direction enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.FootprintPatternType">
            <summary>
            Footprint pattern types
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.MarketSession">
            <summary>
            Market session enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.LiquidityLevel">
            <summary>
            Liquidity level enumeration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RealTickData">
            <summary>
            Real tick data from ATAS live market feeds
            Replaces synthetic buy/sell volume calculations with authentic trade data
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealTickData.IsHighQuality">
            <summary>
            Data quality metrics for live trading validation
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RealOrderBookData">
            <summary>
            Real Level 2 order book data from ATAS market depth feeds
            Replaces simulated order book analysis with authentic market maker data
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealOrderBookData.InstitutionalFlowStrength">
            <summary>
            Institutional flow analysis from real order book data
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealOrderBookData.InstitutionalDirection">
            <summary>
            Dominant institutional direction from real order flow
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealOrderBookData.IsHighQuality">
            <summary>
            Data quality metrics for live trading validation
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RealLargeOrder">
            <summary>
            Real large order from ATAS market depth analysis
            Represents actual institutional-sized orders in the order book
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealLargeOrder.Significance">
            <summary>
            Order significance based on volume and market context
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RealMultiTimeframeData">
            <summary>
            Real multi-timeframe data from ATAS higher timeframe feeds
            Replaces reconstructed MTF data with authentic higher timeframe analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealMultiTimeframeData.CorrelationStrength">
            <summary>
            Correlation strength with current timeframe
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealMultiTimeframeData.IsHighQuality">
            <summary>
            Data quality metrics for live trading validation
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.RealDataValidationResult">
            <summary>
            Real-time data validation result
            Comprehensive validation of all data sources for live trading compliance
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.RealDataValidationResult.LiveTradingCompliant">
            <summary>
            Overall data compliance for live trading
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Models.AustralianEnvironmentMetrics">
            <summary>
            Australian trading environment metrics
            Specialized monitoring for geographical latency and connection quality
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Models.AustralianEnvironmentMetrics.LatencyClassification">
            <summary>
            Latency classification for Australian environment
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Configuration.AssetConfiguration">
            <summary>
            Asset-specific configuration for BTC and ETH futures trading
            
            This class contains all the asset-specific parameters optimized for
            Bitcoin and Ethereum futures, including delta thresholds, volume requirements,
            risk parameters, and market microstructure considerations.
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.TargetAsset">
            <summary>
            Target asset for trading (BTC or ETH)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.TickSize">
            <summary>
            Tick size for the asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.MinVolume">
            <summary>
            Minimum volume for trading
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.MaxVolume">
            <summary>
            Maximum volume for trading
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCDeltaThreshold">
            <summary>
            Delta threshold for BTC futures (buy/sell ratio)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCVolumeThreshold">
            <summary>
            Volume threshold for BTC futures (minimum volume for signal)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCStopLossPercent">
            <summary>
            Stop loss percentage for BTC futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCTakeProfitPercent">
            <summary>
            Take profit percentage for BTC futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCCVDDivergenceThreshold">
            <summary>
            CVD divergence threshold for BTC (minimum percentage)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCMaxLeverage">
            <summary>
            Maximum leverage for BTC futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.BTCAbsorptionThreshold">
            <summary>
            Absorption threshold for BTC (volume concentration)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHDeltaThreshold">
            <summary>
            Delta threshold for ETH futures (buy/sell ratio)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHVolumeThreshold">
            <summary>
            Volume threshold for ETH futures (minimum volume for signal)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHStopLossPercent">
            <summary>
            Stop loss percentage for ETH futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHTakeProfitPercent">
            <summary>
            Take profit percentage for ETH futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHCVDDivergenceThreshold">
            <summary>
            CVD divergence threshold for ETH (minimum percentage)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHMaxLeverage">
            <summary>
            Maximum leverage for ETH futures
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.ETHAbsorptionThreshold">
            <summary>
            Absorption threshold for ETH (volume concentration)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomDeltaThreshold">
            <summary>
            Delta threshold for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomVolumeThreshold">
            <summary>
            Volume threshold for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomStopLossPercent">
            <summary>
            Stop loss percentage for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomTakeProfitPercent">
            <summary>
            Take profit percentage for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomMaxLeverage">
            <summary>
            Maximum leverage for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomCVDDivergenceThreshold">
            <summary>
            CVD divergence threshold for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CustomAbsorptionThreshold">
            <summary>
            Absorption threshold for custom assets (universal setting)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentDeltaThreshold">
            <summary>
            Gets the delta threshold for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentVolumeThreshold">
            <summary>
            Gets the volume threshold for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentStopLossPercent">
            <summary>
            Gets the stop loss percentage for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentTakeProfitPercent">
            <summary>
            Gets the take profit percentage for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentCVDDivergenceThreshold">
            <summary>
            Gets the CVD divergence threshold for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentMaxLeverage">
            <summary>
            Gets the maximum leverage for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.CurrentAbsorptionThreshold">
            <summary>
            Gets the absorption threshold for the current target asset
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.MinSpreadThresholdBP">
            <summary>
            Minimum spread threshold for trade execution (basis points)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.MaxSpreadThresholdBP">
            <summary>
            Maximum spread threshold for trade execution (basis points)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.LiquidityMultiplier">
            <summary>
            Liquidity requirement multiplier
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.AssetConfiguration.OrderBookDepthLevels">
            <summary>
            Order book depth levels to analyze
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.IsValid">
            <summary>
            Validates the asset configuration
            </summary>
            <returns>True if configuration is valid, false otherwise</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.GetAssetSymbol">
            <summary>
            Gets the asset symbol string
            </summary>
            <returns>Asset symbol (BTC or ETH)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.GetRiskRewardRatio">
            <summary>
            Gets the risk-reward ratio for the current asset
            </summary>
            <returns>Risk-reward ratio</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.GetConfigurationSummary">
            <summary>
            Gets a configuration summary for the current asset
            </summary>
            <returns>Configuration summary string</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateConservativeBTC">
            <summary>
            Creates a conservative configuration for BTC
            </summary>
            <returns>Conservative BTC configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateAggressiveBTC">
            <summary>
            Creates an aggressive configuration for BTC
            </summary>
            <returns>Aggressive BTC configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateConservativeETH">
            <summary>
            Creates a conservative configuration for ETH
            </summary>
            <returns>Conservative ETH configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateAggressiveETH">
            <summary>
            Creates an aggressive configuration for ETH
            </summary>
            <returns>Aggressive ETH configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateUniversalCustom">
            <summary>
            Creates a universal configuration for custom assets
            Optimized for altcoins, tokens, and other crypto pairs
            </summary>
            <returns>Universal custom asset configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateConservativeCustom">
            <summary>
            Creates a conservative configuration for custom assets
            Suitable for volatile or unknown tokens
            </summary>
            <returns>Conservative custom asset configuration</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.AssetConfiguration.CreateAggressiveCustom">
            <summary>
            Creates an aggressive configuration for custom assets
            Suitable for high-confidence setups on known tokens
            </summary>
            <returns>Aggressive custom asset configuration</returns>
        </member>
        <member name="T:OrderFlowMasterV1.Configuration.AssetType">
            <summary>
            Supported asset types for order flow analysis
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Configuration.PerformanceMode">
            <summary>
            Performance mode selection for trading optimization
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Configuration.OrderFlowConfiguration">
            <summary>
            Core configuration settings for OrderFlowMaster V1 Strategy
            
            This class contains all the configurable parameters for the order flow analysis,
            including feature toggles, thresholds, and performance settings.
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableFootprintAnalysis">
            <summary>
            Enable/disable footprint analysis component
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableCVDAnalysis">
            <summary>
            Enable/disable Cumulative Volume Delta (CVD) analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableVolumeProfile">
            <summary>
            Enable/disable Volume Profile analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableConfluenceDetection">
            <summary>
            Enable/disable confluence detection system
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CVDPeriod">
            <summary>
            Period for Cumulative Volume Delta calculation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.VolumeProfileLookback">
            <summary>
            Lookback period for Volume Profile analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.ATRPeriod">
            <summary>
            ATR period for volatility calculations
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.ConfluenceThreshold">
            <summary>
            Minimum confluence score required for signal generation (0-100%)
            FIXED: Changed from int to decimal to prevent precision loss in threshold comparisons
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.SignalStrengthThreshold">
            <summary>
            Minimum signal strength required for trade execution (0-100%)
            FIXED: Changed from int to decimal to prevent precision loss in threshold comparisons
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MaxSignalsPerSession">
            <summary>
            Maximum number of signals to process per session
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.SignalCooldownMinutes">
            <summary>
            Minimum time between signals in minutes
            FIXED: Reduced default from 5 to 1 minute for 30-second chart compatibility
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MaxRiskPerTrade">
            <summary>
            Maximum risk per trade as percentage of account balance
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MaxDailyRisk">
            <summary>
            Maximum daily risk as percentage of account balance
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MaxConcurrentPositions">
            <summary>
            Maximum number of concurrent positions
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableDynamicPositionSizing">
            <summary>
            Enable dynamic position sizing based on volatility
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnablePerformanceMonitoring">
            <summary>
            Enable performance monitoring and logging
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableDebugLogging">
            <summary>
            Enable detailed debug logging
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableDebugMode">
            <summary>
            Enable debug mode for development and testing
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.PerformanceWarningThresholdMs">
            <summary>
            Maximum execution time warning threshold in milliseconds
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.VolumeProfileAnalyzerBudgetMs">
            <summary>
            VolumeProfileAnalyzer performance budget in milliseconds (Australian geographical optimization)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.FootprintEngineBudgetMs">
            <summary>
            FootprintEngine performance budget in milliseconds (Australian geographical optimization)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.RiskManagerBudgetMs">
            <summary>
            RiskManager performance budget in milliseconds (Australian geographical optimization)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EntrySignalGeneratorBudgetMs">
            <summary>
            EntrySignalGenerator performance budget in milliseconds (Australian geographical optimization)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.PerformanceModeSelection">
            <summary>
            Performance mode selection - controls analysis depth vs execution speed
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnablePerformanceBudgets">
            <summary>
            Enable or disable performance budget enforcement
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.SafetyMaxExecutionTimeMs">
            <summary>
            Safety valve - maximum allowed execution time even when budgets are disabled
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.UseQualityMode">
            <summary>
            TEMPORARY WORKAROUND: Use Quality Mode (8ms budgets) - RECOMMENDED FOR AUSTRALIAN ENVIRONMENT
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.UseUnlimitedMode">
            <summary>
            TEMPORARY WORKAROUND: Disable all performance budgets for unlimited analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.GetEffectivePerformanceBudget(System.Double)">
            <summary>
            Get effective performance budget based on current mode and component
            </summary>
            <param name="baseComponentBudget">Base budget for the component</param>
            <returns>Effective budget in milliseconds</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.ShouldEnforcePerformanceBudgets">
            <summary>
            Check if performance budget enforcement is active
            </summary>
            <returns>True if budgets should be enforced</returns>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.HistoricalDataCacheSize">
            <summary>
            Cache size for historical data storage
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.LogOutputFolder">
            <summary>
            Custom folder path for logging and data output
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableDataLogging">
            <summary>
            Enable indicator data logging to files
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableSessionFiltering">
            <summary>
            Enable session-based trading filters
            CRYPTO TRADING: Disabled for 24/7 crypto markets
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.AvoidAsianSession">
            <summary>
            Avoid trading during low-volume Asian session
            CRYPTO TRADING: Disabled for 24/7 crypto markets
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.TradingStartTimeUTC">
            <summary>
            Trading start time (UTC)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.TradingEndTimeUTC">
            <summary>
            Trading end time (UTC)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableInstitutionalFlowDetection">
            <summary>
            Enable institutional flow detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.InstitutionalFlowThreshold">
            <summary>
            Institutional flow threshold multiplier
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableAbsorptionDetection">
            <summary>
            Enable absorption pattern detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.AbsorptionSensitivity">
            <summary>
            Absorption detection sensitivity (1-10, higher = more sensitive)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableDivergenceDetection">
            <summary>
            Enable professional order flow divergence detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.DivergenceLookbackPeriod">
            <summary>
            Lookback period for divergence analysis (bars)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.DivergenceMinStrength">
            <summary>
            Minimum divergence strength required (0.0-1.0)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.DivergenceVolumeConfirmation">
            <summary>
            Volume confirmation threshold multiplier for divergence
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableMTFCVDCorrelation">
            <summary>
            Enable multi-timeframe CVD correlation analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MTFCVDCorrelationThreshold">
            <summary>
            Correlation threshold for multi-timeframe validation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MTFCVDLookbackPeriod">
            <summary>
            Lookback period for multi-timeframe CVD analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableBidAskImbalance">
            <summary>
            Enable bid-ask imbalance detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.BidAskImbalanceThreshold">
            <summary>
            Bid-ask imbalance threshold for signal generation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.SignificantImbalanceThreshold">
            <summary>
            Significant imbalance threshold for high-confidence signals
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.ImbalanceLookbackPeriod">
            <summary>
            Lookback period for imbalance analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableLevel2Analysis">
            <summary>
            Enable Level 2 order book analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.LargeOrderThreshold">
            <summary>
            Large order threshold for institutional detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.Level2InstitutionalFlowThreshold">
            <summary>
            Level 2 institutional flow strength threshold
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.OrderBookDepthLevels">
            <summary>
            Order book depth levels to analyze
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableAdvancedVolumeProfile">
            <summary>
            Enable advanced volume profile analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.DynamicLevelThreshold">
            <summary>
            Dynamic level threshold for confluence detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.InstitutionalVolumeThreshold">
            <summary>
            Institutional volume threshold for level detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.ClusterAnalysisPeriod">
            <summary>
            Cluster analysis period for volume clustering
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.VolumeConfluenceThreshold">
            <summary>
            Volume confluence threshold for advanced analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableCVDMultiTimeframeMomentum">
            <summary>
            Enable/disable CVD multi-timeframe momentum analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CVDMomentumAlignmentThreshold">
            <summary>
            Momentum alignment threshold (0-100 scale)
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CVDAccelerationThreshold">
            <summary>
            CVD momentum acceleration threshold
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableFootprintMomentumAcceleration">
            <summary>
            Enable/disable Footprint momentum acceleration analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.FootprintAccelerationThreshold">
            <summary>
            Footprint momentum acceleration threshold
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.BreakoutMomentumThreshold">
            <summary>
            Breakout momentum threshold for footprint analysis
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.InstitutionalMomentumThreshold">
            <summary>
            Institutional momentum threshold for footprint analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.IsValid">
            <summary>
            Validates the configuration settings
            </summary>
            <returns>True if configuration is valid, false otherwise</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.GetConfigurationSummary">
            <summary>
            Gets a summary of the current configuration
            </summary>
            <returns>Configuration summary string</returns>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableMomentumDetector">
            <summary>
            Enable dedicated momentum detector component
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MomentumTrendPeriod">
            <summary>
            Momentum analysis lookback period for trend detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.MomentumThreshold">
            <summary>
            Minimum momentum score required for signal confirmation
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableVolatilityAdjustment">
            <summary>
            Enable volatility-adjusted momentum thresholds
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableBreakoutDetection">
            <summary>
            Enable breakout momentum detection
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.EnableMomentumPerformanceMonitoring">
            <summary>
            Momentum detector performance monitoring
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CreateConservativeConfig">
            <summary>
            Creates a conservative configuration suitable for beginners
            </summary>
            <returns>Conservative configuration instance</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CreateAggressiveConfig">
            <summary>
            Creates an aggressive configuration suitable for experienced traders
            </summary>
            <returns>Aggressive configuration instance</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Configuration.OrderFlowConfiguration.CreateBalancedConfig">
            <summary>
            Creates a balanced configuration suitable for most traders
            </summary>
            <returns>Balanced configuration instance</returns>
        </member>
        <member name="T:OrderFlowMasterV1.OrderFlowMasterV1Strategy">
            <summary>
            OrderFlowMaster V1 Strategy - Professional BTC/ETH Futures Order Flow Trading
            
            Core Features:
            - Footprint analysis with delta detection
            - Cumulative Volume Delta (CVD) tracking
            - Volume profile confluence analysis
            - 4-indicator confluence system for high-accuracy entries
            - Professional risk management with dynamic position sizing
            
            Target Assets: BTC/ETH Futures
            Performance Target: Less than 5ms OnCalculate execution
            Expected Accuracy: 75-85% with proper confluence
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnableStrategy">
             <summary>
             Master control to enable/disable the entire OrderFlow strategy.
            
             **Purpose**: Primary safety switch for strategy activation. When disabled, all analysis continues
             but no trading signals are generated or executed.
            
             **Performance Impact**: Minimal - only affects signal generation, not analysis components.
            
             **Trading Context**:
             - Enable: For live trading and signal generation
             - Disable: For analysis-only mode, system testing, or emergency shutdown
            
             **Crypto-Specific**: Essential for 24/7 crypto markets where manual oversight may be limited.
             Use as primary control for starting/stopping automated trading.
            
             **Recommended**: True for active trading, False for analysis/testing only.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.SelectedAsset">
             <summary>
             Selects the primary cryptocurrency asset for optimized analysis parameters.
            
             **Purpose**: Configures asset-specific analysis parameters, volatility adjustments, and
             risk calculations optimized for the selected cryptocurrency's market characteristics.
            
             **Value Options**:
             - BTC: Bitcoin - Lower volatility, higher liquidity, institutional focus
             - ETH: Ethereum - Higher volatility, DeFi correlation, faster price movements
            
             **Performance Impact**: Affects all analysis components through asset-specific calibration.
             Different assets use optimized thresholds for footprint, CVD, and volume analysis.
            
             **Trading Context**:
             - BTC: Better for conservative strategies, larger position sizes, longer timeframes
             - ETH: Suitable for aggressive strategies, smaller positions, shorter timeframes
            
             **Crypto-Specific**: Each asset has unique order flow characteristics in crypto markets.
             BTC shows more institutional flow patterns, ETH shows more retail/DeFi activity.
            
             **Australian Trading**: Consider asset's primary trading sessions and liquidity patterns
             relative to Australian time zones for optimal signal quality.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CvdPeriod">
             <summary>
             Period for Cumulative Volume Delta (CVD) calculation and trend analysis.
            
             **Purpose**: Controls the lookback period for CVD momentum and divergence detection.
             Higher values provide smoother, more reliable signals but with increased lag.
            
             **Value Ranges**:
             - Conservative (25-40): Smoother signals, less noise, higher lag
             - Balanced (15-25): Good balance of responsiveness and reliability
             - Aggressive (5-15): Fast signals, more noise, minimal lag
            
             **Performance Impact**: Higher values increase calculation time but improve signal quality.
             Each additional period adds ~0.1ms to CVD analysis time.
            
             **Trading Context**:
             - Volatile markets: Use lower values (10-15) for faster reaction
             - Calm markets: Use higher values (25-35) for better trend identification
             - Scalping: 5-10 periods for immediate signals
             - Swing trading: 30-50 periods for trend confirmation
            
             **Crypto-Specific**: Crypto's 24/7 nature allows for consistent CVD accumulation.
             Consider using slightly higher values (20-30) than traditional markets.
            
             **Australian Latency**: With 5-10ms constraints, keep below 30 for optimal performance.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.AtrPeriod">
             <summary>
             Period for Average True Range (ATR) volatility measurement and position sizing.
            
             **Purpose**: Measures market volatility for dynamic stop-loss placement and position sizing.
             ATR adapts risk management to current market conditions automatically.
            
             **Value Ranges**:
             - Short-term (7-10): Responsive to recent volatility changes
             - Standard (14-21): Balanced volatility measurement (recommended)
             - Long-term (25-35): Stable volatility baseline, less reactive
            
             **Performance Impact**: Minimal computational cost (~0.05ms per period).
             Higher values provide more stable volatility readings.
            
             **Trading Context**:
             - High volatility periods: Use shorter periods (10-14) for quick adaptation
             - Stable markets: Use longer periods (21-28) for consistent baselines
             - Risk management: Shorter periods for tighter stops, longer for wider stops
            
             **Interaction Effects**: Works with RiskPerTrade and StopLossPercent for dynamic
             position sizing. Higher ATR periods result in more stable position sizes.
            
             **Crypto-Specific**: Crypto volatility can change rapidly. Standard 14-period
             provides good balance for most crypto pairs and timeframes.
            
             **Australian Trading**: 14-period works well across all major crypto sessions.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.VolumeProfileLookback">
             <summary>
             Lookback period for Volume Profile analysis and key level identification.
            
             **Purpose**: Determines how many bars to analyze for volume-at-price distribution,
             Point of Control (POC), and Value Area calculations. Critical for support/resistance levels.
            
             **Value Ranges**:
             - Short-term (20-40): Recent volume patterns, intraday levels
             - Medium-term (50-100): Session-based analysis, daily levels
             - Long-term (100-200): Multi-session patterns, weekly levels
            
             **Performance Impact**: Significant - each bar requires volume distribution calculation.
             50 bars ≈ 1-2ms, 100 bars ≈ 3-5ms, 200 bars ≈ 8-12ms processing time.
            
             **Trading Context**:
             - Scalping (30-second to 5-minute): 20-50 bars for immediate levels
             - Day trading (15-minute to 1-hour): 50-100 bars for session levels
             - Swing trading (4-hour to daily): 100-200 bars for major levels
            
             **Interaction Effects**: Longer periods provide more significant levels but may
             miss recent volume shifts. Coordinate with your primary trading timeframe.
            
             **Crypto-Specific**: Crypto markets show strong volume profile patterns.
             50-75 bars typically capture 1-2 major volume sessions effectively.
            
             **Australian Latency**: Keep below 100 bars to maintain &lt;5ms performance target.
             Recommended: 50-75 bars for optimal balance of accuracy and speed.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ConfluenceThreshold">
             <summary>
             Minimum percentage of indicators that must align for signal generation.
            
             **Purpose**: Controls signal quality by requiring multiple indicators to confirm
             the same direction. Higher values generate fewer but higher-quality signals.
            
             **Value Ranges**:
             - Conservative (60-80%): High-quality signals, fewer opportunities
             - Balanced (45-60%): Good quality with reasonable frequency (recommended)
             - Aggressive (30-45%): More signals, increased false positives
            
             **Performance Impact**: Minimal computational cost. Affects signal frequency
             and quality rather than processing speed.
            
             **Trading Context**:
             - Trending markets: Lower values (40-50%) to catch momentum early
             - Ranging markets: Higher values (55-70%) to avoid false breakouts
             - High volatility: Increase threshold (50-65%) for better confirmation
             - Low volatility: Decrease threshold (35-50%) for more opportunities
            
             **Interaction Effects**: Works with MinSignalConfidence to create two-tier
             filtering. Both thresholds must be met for signal generation.
            
             **Crypto-Specific**: Crypto markets can show strong directional moves with
             partial indicator alignment. 45-55% range works well for most crypto pairs.
            
             **Australian Trading**: Optimized at 45% for balanced signal generation
             across different crypto market sessions and volatility conditions.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.MinSignalConfidence">
             <summary>
             Minimum confidence level required for individual signal components.
            
             **Purpose**: Sets the quality bar for each signal component (footprint, CVD, volume profile).
             Acts as a pre-filter before confluence analysis to ensure base signal quality.
            
             **Value Ranges**:
             - High quality (50-70%): Very selective, premium signals only
             - Medium quality (35-50%): Balanced approach, good signal flow
             - High frequency (25-35%): More signals, requires careful risk management
            
             **Performance Impact**: Affects signal generation frequency significantly.
             Lower values can increase signals by 2-3x but may reduce win rate.
            
             **Trading Context**:
             - Strong trending markets: Lower values (30-40%) to capture momentum
             - Choppy markets: Higher values (45-60%) to avoid noise
             - News/event periods: Increase temporarily (50-65%) for stability
             - Quiet periods: Decrease (30-45%) to maintain signal flow
            
             **Interaction Effects**: Combined with ConfluenceThreshold creates robust
             two-stage filtering. Both individual components AND overall confluence must pass.
            
             **Crypto-Specific**: Crypto order flow can be more erratic than traditional markets.
             35-45% range provides good balance between signal quality and frequency.
            
             **Australian Trading**: Set at 35% to accommodate varying global crypto
             liquidity patterns while maintaining reasonable signal quality standards.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.RiskPerTrade">
             <summary>
             Maximum percentage of account equity to risk on each individual trade.
            
             **Purpose**: Controls position sizing to limit maximum loss per trade. Combined with
             ATR-based stop losses to calculate appropriate position size for each signal.
            
             **Value Ranges**:
             - Conservative (0.5-1.0%): Capital preservation, steady growth
             - Moderate (1.0-2.0%): Balanced risk/reward, recommended for most traders
             - Aggressive (2.0-5.0%): Higher returns, requires excellent risk management
            
             **Performance Impact**: No computational impact. Affects position sizing calculations
             and overall portfolio risk exposure.
            
             **Trading Context**:
             - Account building phase: Start conservative (0.5-1.0%)
             - Experienced trading: Moderate approach (1.0-2.0%)
             - High-conviction setups: Can increase temporarily (up to 3.0%)
             - Drawdown periods: Reduce risk (0.5-1.0%) until recovery
            
             **Interaction Effects**: Works with MaxDailyRisk to create position-level and
             daily-level risk controls. Also interacts with ATR for dynamic stop placement.
            
             **Crypto-Specific**: Crypto volatility requires careful position sizing.
             1.0-1.5% provides good balance between growth and preservation for most crypto traders.
            
             **Australian Trading**: Consider overnight gaps and weekend crypto volatility.
             Recommended: 1.0% for consistent risk management across 24/7 crypto markets.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.MaxDailyRisk">
             <summary>
             Maximum percentage of account equity that can be at risk across all open positions daily.
            
             **Purpose**: Portfolio-level risk control to prevent excessive exposure during
             high-activity periods. Stops new position opening when daily risk limit is reached.
            
             **Value Ranges**:
             - Conservative (3-5%): Strict portfolio protection, limited concurrent positions
             - Moderate (5-10%): Balanced exposure, allows multiple positions
             - Aggressive (10-20%): High exposure, requires excellent market timing
            
             **Performance Impact**: No computational cost. Controls trading frequency and
             maximum concurrent position exposure.
            
             **Trading Context**:
             - Trending markets: Can use higher limits (7-12%) for momentum capture
             - Volatile markets: Reduce limits (3-7%) for protection
             - News/event periods: Temporarily reduce (2-5%) for safety
             - Quiet periods: Standard limits (5-8%) for normal operation
            
             **Interaction Effects**: Must be higher than RiskPerTrade to allow multiple positions.
             Recommended ratio: MaxDailyRisk = 3-5x RiskPerTrade for balanced exposure.
            
             **Crypto-Specific**: 24/7 crypto markets can generate many signals. Daily risk
             limits prevent overexposure during high-activity periods or trending moves.
            
             **Australian Trading**: Set at 5.0% to allow 3-5 concurrent positions while
             maintaining prudent risk management for continuous crypto market exposure.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnableFootprintAnalysis">
             <summary>
             Enable/disable footprint analysis for order flow detection and absorption patterns.
            
             **Purpose**: Controls the most computationally intensive analysis component.
             Footprint analysis detects large order absorption, bid/ask imbalances, and institutional flow.
            
             **Performance Impact**: HIGH - Footprint analysis typically consumes 40-60% of total
             processing time (~2-4ms). Disabling can significantly improve performance.
            
             **Trading Context**:
             - Enable: For complete order flow analysis and highest signal quality
             - Disable: When performance is critical or during high-frequency periods
             - Scalping: Consider disabling for sub-second timeframes
             - Swing trading: Always enable for comprehensive analysis
            
             **Interaction Effects**: Disabling reduces available confirming indicators from 4 to 3,
             which may affect confluence calculations and signal generation frequency.
            
             **Crypto-Specific**: Crypto footprint patterns are highly valuable for detecting
             institutional accumulation/distribution. Recommended to keep enabled unless performance critical.
            
             **Australian Latency**: With 5-10ms constraints, consider disabling during peak
             volatility periods if OnCalculate times exceed 8ms consistently.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnableCVDAnalysis">
             <summary>
             Enable/disable Cumulative Volume Delta (CVD) analysis for trend and momentum detection.
            
             **Purpose**: Controls CVD calculation for buy/sell pressure analysis and divergence detection.
             CVD is essential for identifying momentum shifts and trend strength.
            
             **Performance Impact**: MEDIUM - CVD analysis consumes ~15-25% of processing time
             (~0.5-1.5ms). Moderate performance benefit when disabled.
            
             **Trading Context**:
             - Enable: For momentum-based strategies and trend following
             - Disable: For pure price action or mean reversion strategies
             - Trending markets: Essential for momentum confirmation
             - Range-bound markets: Less critical, can disable for performance
            
             **Interaction Effects**: CVD provides crucial momentum confirmation for other indicators.
             Disabling may reduce signal quality, especially in trending conditions.
            
             **Crypto-Specific**: Crypto markets show strong CVD patterns due to retail vs institutional
             flow differences. Highly recommended for crypto trading strategies.
            
             **Australian Trading**: CVD works well across all crypto sessions. Keep enabled
             unless severe performance constraints require optimization.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnableVolumeProfile">
             <summary>
             Enable/disable Volume Profile analysis for support/resistance and value area detection.
            
             **Purpose**: Controls volume-at-price analysis for identifying key levels, Point of Control (POC),
             and Value Area boundaries. Critical for level-based trading strategies.
            
             **Performance Impact**: MEDIUM-HIGH - Volume Profile analysis consumes ~20-35% of
             processing time (~1-3ms depending on lookback period).
            
             **Trading Context**:
             - Enable: For level-based trading and support/resistance strategies
             - Disable: For pure momentum or trend-following approaches
             - Range markets: Essential for identifying key levels
             - Breakout trading: Critical for confirming level significance
            
             **Interaction Effects**: Volume Profile provides key levels for entry/exit decisions.
             Other indicators use these levels for confluence analysis.
            
             **Crypto-Specific**: Crypto markets respect volume profile levels very well due to
             algorithmic trading and institutional participation. Highly valuable for crypto analysis.
            
             **Australian Trading**: Volume Profile levels remain valid across global crypto sessions.
             Recommended to keep enabled for comprehensive level analysis.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.DebugMode">
             <summary>
             Enable detailed debug logging for troubleshooting and system analysis.
            
             **Purpose**: Activates comprehensive logging of all analysis components, signal generation
             steps, and performance metrics. Essential for optimization and troubleshooting.
            
             **Performance Impact**: LOW-MEDIUM - Debug logging adds ~0.2-0.5ms per OnCalculate
             due to string formatting and file I/O operations.
            
             **Trading Context**:
             - Enable: During strategy development, optimization, or troubleshooting
             - Disable: For production trading to minimize performance overhead
             - Testing: Always enable for backtesting and forward testing
             - Live trading: Disable unless investigating specific issues
            
             **Interaction Effects**: Provides detailed visibility into all component decisions
             and performance metrics. Essential for understanding signal generation logic.
            
             **Crypto-Specific**: Crypto's 24/7 nature can generate large log files quickly.
             Monitor disk space when enabled for extended periods.
            
             **Australian Trading**: Useful for analyzing performance across different global
             crypto sessions and identifying optimal trading periods.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnablePerformanceMonitoring">
             <summary>
             Enable real-time performance monitoring and budget enforcement.
            
             **Purpose**: Monitors OnCalculate execution times and enforces performance budgets.
             Provides warnings when components exceed time limits and enables graceful degradation.
            
             **Performance Impact**: MINIMAL - Performance monitoring adds ~0.05-0.1ms overhead
             but prevents much larger performance issues through early detection.
            
             **Trading Context**:
             - Enable: Always recommended for live trading and performance optimization
             - Disable: Only for backtesting where performance monitoring isn't needed
             - High-frequency: Essential for maintaining consistent execution times
             - Development: Critical for identifying performance bottlenecks
            
             **Interaction Effects**: Enables automatic performance budget enforcement across
             all analysis components. Prevents any single component from degrading overall performance.
            
             **Crypto-Specific**: 24/7 crypto markets require consistent performance. Monitoring
             helps maintain stable execution across varying market conditions and volatility.
            
             **Australian Latency**: Essential for 5-10ms latency constraints. Provides early
             warning when approaching performance limits and enables proactive optimization.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.SizingMode">
             <summary>
             Selects the method for calculating position sizes for each trade.
            
             **Purpose**: Determines how position sizes are calculated - either based on risk percentage
             of account equity or fixed dollar amounts. Critical for consistent risk management.
            
             **Value Options**:
             - RiskPercentage: Uses RiskPerTrade% with ATR-based stops for dynamic sizing
             - FixedAmount: Uses FixedUsdtAmount for consistent dollar exposure
             - LegacySize: Uses PositionSize for backward compatibility
            
             **Performance Impact**: Minimal computational cost. Affects position calculation
             method and risk management approach significantly.
            
             **Trading Context**:
             - RiskPercentage: Best for account growth and consistent risk management
             - FixedAmount: Good for testing or when account size varies frequently
             - LegacySize: Only for backward compatibility with older configurations
            
             **Interaction Effects**: RiskPercentage mode uses RiskPerTrade, ATR, and StopLoss
             for dynamic sizing. FixedAmount mode ignores risk percentage settings.
            
             **Crypto-Specific**: RiskPercentage recommended for crypto due to varying volatility.
             Automatically adjusts position sizes based on current market conditions.
            
             **Australian Trading**: RiskPercentage mode recommended for professional
             risk management and consistent account growth across varying market conditions.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.FixedUsdtAmount">
             <summary>
             Fixed dollar amount (USDT) to risk per trade when using FixedAmount sizing mode.
            
             **Purpose**: Sets a consistent dollar amount for each position when using fixed
             sizing mode. Provides predictable exposure regardless of market volatility.
            
             **Value Ranges**:
             - Small accounts ($10-50): Conservative testing and learning
             - Medium accounts ($50-200): Balanced exposure for most traders
             - Large accounts ($200-1000): Higher exposure for experienced traders
            
             **Performance Impact**: No computational cost. Only used when SizingMode
             is set to FixedAmount, otherwise ignored.
            
             **Trading Context**:
             - Testing: Use small amounts ($10-25) for strategy validation
             - Live trading: Scale based on account size and risk tolerance
             - Volatile periods: Consider reducing fixed amounts temporarily
             - Stable periods: Can use standard amounts for consistent exposure
            
             **Interaction Effects**: Only active when SizingMode = FixedAmount.
             Overrides all risk percentage and ATR-based sizing calculations.
            
             **Crypto-Specific**: Fixed amounts work well for crypto testing but may not
             adapt to changing volatility. Consider RiskPercentage for live trading.
            
             **Australian Trading**: $10 USDT provides reasonable testing exposure.
             Scale up based on account size and experience level.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.PositionSize">
             <summary>
             Legacy position size in native asset units (ETH/BTC) for backward compatibility.
            
             **Purpose**: Maintains compatibility with older strategy configurations.
             Specifies position size directly in asset units (e.g., 0.01 BTC, 0.1 ETH).
            
             **Value Ranges**:
             - BTC: 0.001-1.0 (typical range for most account sizes)
             - ETH: 0.01-10.0 (typical range for most account sizes)
            
             **Performance Impact**: No computational cost. Only used when SizingMode
             is set to LegacySize for backward compatibility.
            
             **Trading Context**:
             - Legacy systems: For maintaining existing strategy configurations
             - Simple sizing: When advanced risk management isn't needed
             - Fixed exposure: For consistent asset-based position sizes
            
             **Interaction Effects**: Only active when SizingMode = LegacySize.
             Bypasses all modern risk management and dynamic sizing features.
            
             **Crypto-Specific**: Direct asset sizing doesn't adapt to price changes.
             0.01 BTC has different dollar value at $30k vs $60k BTC price.
            
             **Australian Trading**: Not recommended for new strategies. Use RiskPercentage
             mode for better risk management and account growth consistency.
            
             **Deprecation Notice**: This parameter exists for backward compatibility only.
             New strategies should use RiskPercentage or FixedAmount modes.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.TakeProfitPercent">
             <summary>
             Percentage profit target for automatic position closure.
            
             **Purpose**: Sets the profit target as a percentage of entry price. When reached,
             the position is automatically closed with a limit order to lock in profits.
            
             **Value Ranges**:
             - Conservative (1.0-2.0%): High probability targets, frequent profit taking
             - Moderate (2.0-4.0%): Balanced risk/reward, good for most strategies
             - Aggressive (4.0-10.0%): High reward targets, lower hit rate
            
             **Performance Impact**: No computational cost. Affects trade profitability
             and position holding time significantly.
            
             **Trading Context**:
             - Trending markets: Higher targets (3-6%) to capture momentum
             - Range-bound markets: Lower targets (1-3%) for quick profits
             - High volatility: Adjust based on ATR - typically 2-4x daily ATR
             - Low volatility: Smaller targets (1-2%) for consistent profits
            
             **Interaction Effects**: Should maintain favorable risk/reward ratio with StopLossPercent.
             Recommended ratio: TakeProfit = 1.5-3.0x StopLoss for positive expectancy.
            
             **Crypto-Specific**: Crypto volatility allows for larger profit targets.
             2-4% targets are realistic for most crypto pairs on intraday timeframes.
            
             **Australian Trading**: Set at 2.0% for balanced profit capture across
             varying crypto volatility periods and global session transitions.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.StopLossPercent">
             <summary>
             Percentage loss limit for automatic position closure and capital protection.
            
             **Purpose**: Sets the maximum acceptable loss as a percentage of entry price.
             When reached, position is closed immediately with a stop order to limit losses.
            
             **Value Ranges**:
             - Tight stops (0.5-1.0%): Capital preservation, higher stop-out frequency
             - Standard stops (1.0-2.0%): Balanced protection, reasonable breathing room
             - Wide stops (2.0-5.0%): Trend following, lower stop-out frequency
            
             **Performance Impact**: No computational cost. Critical for risk management
             and overall strategy profitability through loss limitation.
            
             **Trading Context**:
             - Volatile markets: Wider stops (1.5-3.0%) to avoid noise-based exits
             - Calm markets: Tighter stops (0.5-1.5%) for precise risk control
             - Trend following: Wider stops (2-4%) to allow for pullbacks
             - Mean reversion: Tighter stops (0.5-1.5%) for quick exits
            
             **Interaction Effects**: Must maintain positive risk/reward with TakeProfitPercent.
             Also interacts with RiskPerTrade for position sizing calculations.
            
             **Crypto-Specific**: Crypto's high volatility requires careful stop placement.
             1.0-2.0% stops provide good balance between protection and avoiding false exits.
            
             **Australian Trading**: Set at 1.0% for disciplined risk management.
             Tight enough for capital protection, wide enough for crypto volatility.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnableTakeProfit">
             <summary>
             Master control for automatic Take Profit and Stop Loss order placement.
            
             **Purpose**: Enables/disables automatic TP/SL order placement after entry fills.
             When enabled, both profit target and stop loss orders are placed immediately after entry.
            
             **Performance Impact**: Minimal computational cost. Affects order management
             complexity and requires additional order tracking and monitoring.
            
             **Trading Context**:
             - Enable: For automated risk management and hands-off trading
             - Disable: For manual position management or complex exit strategies
             - Scalping: Enable for quick, automated exits
             - Position trading: Consider manual management for flexibility
            
             **Interaction Effects**: When enabled, uses TakeProfitPercent and StopLossPercent
             for automatic order placement. Requires proper order management and tracking.
            
             **Crypto-Specific**: Essential for 24/7 crypto markets where manual monitoring
             is impractical. Provides consistent risk management across all trading sessions.
            
             **Australian Trading**: Highly recommended for Australian traders due to
             time zone differences with major crypto liquidity centers. Ensures protection
             during sleep hours and provides automated profit capture.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GenerateTestSignals">
             <summary>
             Enable generation of synthetic test signals for strategy validation and testing.
            
             **Purpose**: Creates artificial trading signals at regular intervals for testing
             order execution, risk management, and system integration without waiting for real signals.
            
             **Performance Impact**: Minimal computational cost. Generates predictable signals
             for testing purposes, bypassing normal signal generation logic.
            
             **Trading Context**:
             - Enable: For testing order execution, TP/SL placement, and system integration
             - Disable: For live trading and real signal generation (normal operation)
             - Development: Essential for validating order management and risk systems
             - Production: Must be disabled for live trading
            
             **Interaction Effects**: When enabled, overrides normal signal generation and
             creates test signals regardless of market conditions or indicator states.
            
             **Crypto-Specific**: Test signals help validate 24/7 operation and order
             management across different crypto market conditions and volatility periods.
            
             **Australian Trading**: Useful for testing during Australian hours when
             crypto volatility may be lower and natural signals less frequent.
            
             **CRITICAL WARNING**: Must be set to FALSE for live trading. Test signals
             will execute real trades with real money if enabled in production.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ROCPeriod">
             <summary>
             Period for Rate of Change (ROC) momentum indicator calculation.
            
             **Purpose**: Determines the lookback period for measuring price momentum as percentage change.
             ROC measures the speed of price change over the specified period for momentum confirmation.
            
             **Value Ranges**:
             - Short-term (5-10): Sensitive to recent momentum changes, good for scalping
             - Medium-term (10-20): Balanced momentum measurement, recommended for most strategies
             - Long-term (20-30): Stable momentum trends, good for swing trading
            
             **Performance Impact**: Minimal - simple percentage calculation with O(1) complexity.
            
             **Trading Context**:
             - Crypto markets: 10-15 period captures momentum well for most timeframes
             - Australian trading: 12 period optimized for crypto session patterns
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.VolumeOscillatorFastPeriod">
             <summary>
             Fast period for Volume Oscillator calculation.
            
             **Purpose**: Short-term volume moving average for volume momentum analysis.
             Volume Oscillator compares fast vs slow volume averages to detect volume momentum shifts.
            
             **Value Ranges**:
             - Very fast (3-7): Highly responsive to volume changes
             - Fast (7-14): Good sensitivity for most crypto pairs
             - Standard (14-21): Balanced approach with less noise
            
             **Performance Impact**: Minimal - simple moving average calculation.
            
             **Trading Context**:
             - Scalping: Use faster periods (5-10) for quick volume momentum detection
             - Day trading: Standard periods (10-14) for reliable volume signals
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.VolumeOscillatorSlowPeriod">
             <summary>
             Slow period for Volume Oscillator calculation.
            
             **Purpose**: Long-term volume moving average for volume momentum baseline.
             Must be larger than fast period to create meaningful oscillator signals.
            
             **Value Ranges**:
             - Standard (14-28): Good baseline for most strategies
             - Long-term (28-50): Stable baseline, less sensitive to noise
            
             **Performance Impact**: Minimal - simple moving average calculation.
            
             **Interaction Effects**: Must be significantly larger than fast period (typically 2-3x).
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.MACDFastPeriod">
             <summary>
             Fast EMA period for MACD momentum indicator.
            
             **Purpose**: Short-term exponential moving average for MACD calculation.
             MACD measures momentum through the difference between fast and slow EMAs.
            
             **Value Ranges**:
             - Fast (8-12): Highly responsive to price changes
             - Standard (12-15): Traditional MACD settings
             - Conservative (15-20): Less sensitive, fewer false signals
            
             **Performance Impact**: Minimal - exponential moving average calculation.
            
             **Trading Context**: 12-period is the traditional MACD fast period, optimized for most markets.
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.MACDSlowPeriod">
             <summary>
             Slow EMA period for MACD momentum indicator.
            
             **Purpose**: Long-term exponential moving average for MACD calculation.
             Must be larger than fast period to create meaningful MACD signals.
            
             **Value Ranges**:
             - Standard (20-26): Traditional MACD settings
             - Long-term (26-35): More stable, fewer signals
            
             **Performance Impact**: Minimal - exponential moving average calculation.
            
             **Interaction Effects**: Must be larger than fast period (typically 2-3x).
             </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.MACDSignalPeriod">
             <summary>
             Signal line EMA period for MACD momentum indicator.
            
             **Purpose**: Signal line smoothing for MACD histogram and crossover signals.
             Signal line is an EMA of the MACD line used for entry/exit timing.
            
             **Value Ranges**:
             - Fast (5-9): Quick signals, more noise
             - Standard (9-12): Traditional MACD signal settings
             - Smooth (12-15): Fewer signals, better quality
            
             **Performance Impact**: Minimal - exponential moving average calculation.
            
             **Trading Context**: 9-period is the traditional MACD signal period, providing good balance.
             </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.#ctor">
            <summary>
            Initialize strategy components and indicators
            ATAS Compliance: Must call base constructor with useCandles=true for ChartStrategy
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.InitializeConfigurations">
            <summary>
            Initialize strategy configurations
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.InitializeATASIndicators">
            <summary>
            Initialize ATAS indicators including Phase 2 momentum indicators
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.InitializeComponents">
            <summary>
            Initialize core strategy components
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnStarted">
            <summary>
            Called when strategy is started - reinitialize with loaded parameters
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.PerformLiveDataDiagnostics">
            <summary>
            Perform comprehensive live data connection diagnostics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Main calculation method - called for each new bar/tick
            Performance Target: Less than 5ms execution time
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetTradesCacheForServices(System.TimeSpan)">
            <summary>
            Wrapper method to expose protected GetTradesCache method to services
            Based on ATAS documentation: GetTradesCache(TimeSpan period) returns ITradesCache
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.PerformOrderFlowAnalysis(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Perform comprehensive order flow analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.PerformPhase3Optimization(System.Int32,ATAS.Indicators.IndicatorCandle,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            🎯 PHASE 3: Perform real-time optimization and analytics
            Target: Less than 2ms execution time for all Phase 3 components
            </summary>
            <param name="bar">Current bar index</param>
            <param name="candle">Current candle data</param>
            <param name="signal">Generated signal (if any)</param>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ApplyRegimeAdjustments(OrderFlowMasterV1.Components.RegimeAdjustments)">
            <summary>
            Apply regime-specific adjustments to strategy parameters
            </summary>
            <param name="adjustments">Regime adjustments to apply</param>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CreateMarketCondition(ATAS.Indicators.IndicatorCandle)">
            <summary>
            Create market condition data for optimization
            </summary>
            <param name="candle">Current candle</param>
            <returns>Market condition data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CreatePerformanceMetric">
            <summary>
            Create performance metric for optimization
            </summary>
            <returns>Performance metric data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CreateTradeResultFromSignal(OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Create trade result from signal for analytics
            </summary>
            <param name="signal">Order flow signal</param>
            <returns>Trade result data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ProcessSignal(System.Int32,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Process generated signal and execute trades
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.UpdateChartVisualization(System.Int32,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Update chart visualization with signal data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.LogSignalDetails(System.Int32,OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Log detailed signal information for debugging
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CleanupSignalHistory(System.Int32)">
            <summary>
            Clean up old signal history to manage memory
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CreateTestSignal(System.Int32,ATAS.Indicators.IndicatorCandle)">
            <summary>
            Create a test signal for verification purposes
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.TriggerEmergencyStop(System.String,System.Int32)">
            <summary>
            Emergency stop method triggered when fake/synthetic data is detected
            CRITICAL: Immediately stops the strategy to prevent live trading with non-real data
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.OrderFlowMasterV1Strategy.IsEmergencyStopTriggered">
            <summary>
            Check if emergency stop has been triggered
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetMarketDepthSnapshotForServices">
            <summary>
            CRITICAL: Get real market depth snapshot from ATAS
            Uses ATAS GetMarketDepthSnapshot() API for authentic Level 2 data
            Based on ATAS documentation: Returns IEnumerable&lt;MarketDataArg&gt;
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetBestBidForServices">
            <summary>
            CRITICAL: Get real best bid from ATAS
            Uses ATAS BestBid property for authentic bid data
            Based on ATAS documentation: BestBid property from ChartStrategy
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetBestAskForServices">
            <summary>
            CRITICAL: Get real best ask from ATAS
            Uses ATAS BestAsk property for authentic ask data
            Based on ATAS documentation: BestAsk property from ChartStrategy
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetCandleForServices(System.Int32)">
            <summary>
            CRITICAL: Get candle for services
            Uses ATAS GetCandle() method for authentic candle data
            Based on ATAS documentation: GetCandle(int bar) from ExtendedIndicator
            ENHANCED: Improved boundary checking and connection validation
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetRealCumulativeTradesForServices(System.Int32)">
            <summary>
            CRITICAL: Get real cumulative trades from ATAS
            Uses ATAS RequestForCumulativeTrades() and OnCumulativeTradesResponse() for authentic tick data
            Based on ATAS documentation: CumulativeTradesRequest and response handling
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnCumulativeTradesResponse(ATAS.Indicators.CumulativeTradesRequest,System.Collections.Generic.IEnumerable{ATAS.Indicators.CumulativeTrade})">
            <summary>
            CRITICAL: Handle cumulative trades response from ATAS
            Based on ATAS documentation: OnCumulativeTradesResponse override
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CumulativeTradesRequestInfo">
            <summary>
            Helper class for tracking cumulative trades requests
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetRealMultiTimeframeDataForServices(System.DateTime,System.TimeSpan)">
            <summary>
            CRITICAL: Get real multi-timeframe data from ATAS (OPTIMIZED)
            FIXED: Eliminates "Index was out of range" errors with proper bounds checking
            Uses reasonable aggregation periods and comprehensive error handling
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetReasonableAggregationPeriod(System.TimeSpan,System.Int32)">
            <summary>
            OPTIMIZED: Get reasonable aggregation period for timeframe without causing index errors
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.AggregateHistoricalCandles(System.Int32,System.Int32)">
            <summary>
            OPTIMIZED: Safely aggregate historical candles with comprehensive bounds checking
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CandleAggregationResult">
            <summary>
            Helper class for candle aggregation results
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.AddEmergencyDataSafetyConfiguration">
            <summary>
            CRITICAL: Add emergency data safety configuration
            Configures strict real market data compliance settings
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetConfigValue``1(System.String,``0)">
            <summary>
            Get value from emergency configuration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.SetConfigValue``1(System.String,``0)">
            <summary>
            Set value in emergency configuration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.DisposeComponents">
            <summary>
            Dispose all components to prevent further processing
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetATASROC(System.Int32)">
            <summary>
            Get ATAS Rate of Change (ROC) indicator value with real data validation
            Phase 2 implementation following ATAS documentation compliance
            </summary>
            <param name="bar">Bar index for calculation</param>
            <returns>ROC value as percentage change, 0 if validation fails</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetATASVolumeOscillator(System.Int32)">
            <summary>
            Get custom Volume Momentum value with real data validation
            Phase 2 implementation using ATAS Volume indicator for momentum calculation
            </summary>
            <param name="bar">Bar index for calculation</param>
            <returns>Volume momentum value as percentage change, 0 if validation fails</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetATASMACD(System.Int32)">
            <summary>
            Get ATAS MACD indicator values with real data validation
            Phase 2 implementation following ATAS documentation compliance
            </summary>
            <param name="bar">Bar index for calculation</param>
            <returns>Dictionary containing MACD, Signal, and Histogram values</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ValidateATASConnection">
            <summary>
            Validate ATAS connection and indicator readiness
            </summary>
            <returns>True if ATAS connection is valid and indicators are ready</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CalculateVolumeMA(System.Int32,System.Int32)">
            <summary>
            Calculate volume moving average for the specified period
            Phase 2 helper method for custom volume momentum calculation
            </summary>
            <param name="bar">Current bar index</param>
            <param name="period">Moving average period</param>
            <returns>Volume moving average value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ValidateIndicatorDataSource(System.Decimal,System.String,System.Int32)">
            <summary>
            Validate indicator data source for real data compliance
            </summary>
            <param name="value">Indicator value to validate</param>
            <param name="indicatorName">Name of the indicator for logging</param>
            <param name="bar">Bar index for logging</param>
            <returns>True if data source is valid</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CalculatePositionSize(OrderFlowMasterV1.Models.OrderFlowSignal,System.Decimal)">
            <summary>
            Calculate position size based on selected sizing mode
            </summary>
            <param name="signal">Trading signal with entry price</param>
            <param name="currentPrice">Current market price</param>
            <returns>Position size in asset units (ETH/BTC)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetCurrentMarketPrice(System.Int32)">
            <summary>
            Get current market price for position sizing calculations
            </summary>
            <param name="bar">Current bar index</param>
            <returns>Current price or signal entry price as fallback</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ExecuteTrade(OrderFlowMasterV1.Models.OrderFlowSignal,System.Int32)">
            <summary>
            Execute trade based on signal using REAL LIVE market data
            </summary>
            <param name="signal">Trading signal from live market analysis</param>
            <param name="bar">Current bar index</param>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.PlaceTakeProfitAndStopLoss(ATAS.DataFeedsCore.Order)">
            <summary>
            Place Take Profit and Stop Loss orders after entry fill
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.CancelAllOrders(System.String)">
            <summary>
            Cancel all pending orders
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ValidatePositionState(OrderFlowMasterV1.Models.OrderFlowSignal)">
            <summary>
            Comprehensive position state validation to prevent multiple simultaneous trades
            </summary>
            <param name="signal">Trading signal to validate</param>
            <returns>True if trade can proceed, false if blocked</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ClosePosition(System.String)">
            <summary>
            Close current position if needed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnOrderChanged(ATAS.DataFeedsCore.Order)">
            <summary>
            Handle order state changes and manage TP/SL orders
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnOrderRegisterFailed(ATAS.DataFeedsCore.Order,System.String)">
            <summary>
            Handle order registration failures - Critical for debugging TP/SL issues
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnStopping">
            <summary>
            Override OnStopping to close positions safely
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnStopped">
            <summary>
            Override OnStopped to ensure complete shutdown
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ValidateRealMarketData(ATAS.Indicators.IndicatorCandle,System.Int32)">
            <summary>
            Validates that we are using REAL LIVE market data only
            NO MOCK, FAKE, OR SIMULATED DATA ALLOWED
            </summary>
            <param name="candle">Candle to validate</param>
            <param name="bar">Bar index</param>
            <returns>True if data is valid live market data</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.ValidateATASDataSource">
            <summary>
            Validates that ATAS platform is providing real market data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.GetATRValue(System.Int32)">
            <summary>
            Get ATR value for volatility analysis
            Phase 3 helper method for MomentumDetector
            </summary>
            <param name="bar">Bar index</param>
            <returns>ATR value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.OnDispose">
            <summary>
            Cleanup resources when strategy is disposed
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.OrderFlowMasterV1Strategy.EnsureVolumeCompliance(System.Decimal)">
            <summary>
            Ensures order volume complies with ATAS instrument requirements
            Prevents "Qty invalid" errors by rounding to appropriate LotSize
            </summary>
            <param name="volume">Original calculated volume</param>
            <returns>Volume compliant with instrument specifications</returns>
        </member>
        <member name="T:OrderFlowMasterV1.PositionSizingMode">
            <summary>
            Position sizing calculation modes
            </summary>
        </member>
        <member name="F:OrderFlowMasterV1.PositionSizingMode.RiskPercentage">
            <summary>
            Use risk percentage of account balance
            </summary>
        </member>
        <member name="F:OrderFlowMasterV1.PositionSizingMode.FixedUsdtAmount">
            <summary>
            Use fixed USDT amount for position sizing
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.AssetType">
            <summary>
            Asset type enumeration for strategy configuration
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Services.DataLoggingService">
            <summary>
            Service for logging indicator data and analysis results to custom folder
            Ensures all data comes from REAL LIVE MARKET DATA only
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.#ctor(ATAS.Strategies.Chart.ChartStrategy,OrderFlowMasterV1.Configuration.OrderFlowConfiguration)">
            <summary>
            Initialize the data logging service
            </summary>
            <param name="strategy">Chart strategy instance for LIVE market data access</param>
            <param name="config">Configuration settings</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogSignal(OrderFlowMasterV1.Models.OrderFlowSignal,System.Int32)">
            <summary>
            Log order flow signal data from LIVE market
            </summary>
            <param name="signal">Signal generated from real market data</param>
            <param name="bar">Current bar index</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogFootprintData(System.Int32,System.Decimal,System.Decimal,System.Boolean,System.Decimal,System.Boolean,System.Decimal,System.Decimal)">
            <summary>
            Log footprint data from LIVE market with momentum acceleration data
            CRITICAL FIX: Enhanced logging to include momentum acceleration data for debugging
            </summary>
            <param name="bar">Bar index</param>
            <param name="deltaValue">Delta value from real market data</param>
            <param name="volume">Volume from real market data</param>
            <param name="absorption">Absorption detected from real market data</param>
            <param name="momentumAcceleration">Momentum acceleration rate</param>
            <param name="hasMomentumBuilding">True if momentum is building</param>
            <param name="breakoutMomentum">Breakout momentum strength</param>
            <param name="institutionalMomentum">Institutional momentum strength</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogFootprintData(System.Int32,System.Decimal,System.Decimal,System.Boolean)">
            <summary>
            Backward compatibility overload for LogFootprintData
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogCVDData(System.Int32,System.Decimal,System.Boolean,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal)">
            <summary>
            Log CVD data from LIVE market with momentum enhancement data
            CRITICAL FIX: Enhanced logging to include momentum data for debugging
            </summary>
            <param name="bar">Bar index</param>
            <param name="cvdValue">CVD value from real market data</param>
            <param name="divergence">Divergence detected from real market data</param>
            <param name="shortTermMomentum">Short-term momentum (5-bar)</param>
            <param name="mediumTermMomentum">Medium-term momentum (15-bar)</param>
            <param name="longTermMomentum">Long-term momentum (30-bar)</param>
            <param name="momentumAlignment">Momentum alignment score</param>
            <param name="momentumAcceleration">Momentum acceleration rate</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogCVDData(System.Int32,System.Decimal,System.Boolean)">
            <summary>
            Backward compatibility overload for LogCVDData
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogVolumeProfileData(System.Int32,System.Decimal,System.Boolean)">
            <summary>
            Log volume profile data from LIVE market
            </summary>
            <param name="bar">Bar index</param>
            <param name="poc">Point of Control from real market data</param>
            <param name="highVolumeNode">High volume node detected</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogPerformance(System.String,System.Double,System.Int32,System.String)">
            <summary>
            Log performance metrics with enhanced component-specific data
            CRITICAL FIX: Enhanced logging to include component-specific performance metrics
            </summary>
            <param name="component">Component name</param>
            <param name="executionTimeMs">Execution time in milliseconds</param>
            <param name="bar">Current bar</param>
            <param name="additionalMetrics">Additional component-specific metrics</param>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.LogPerformance(System.String,System.Double,System.Int32)">
            <summary>
            Backward compatibility overload for LogPerformance
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.GetLogFolderPath">
            <summary>
            Get the log folder path based on configuration
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.DataLoggingService.InitializeLogFiles">
            <summary>
            Initialize log files with headers
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Services.RealDataAccessLayer">
            <summary>
            Real Data Access Layer for 100% live market data compliance
            Provides access to real ATAS tick data, order book data, and multi-timeframe data
            Replaces all simulated, synthetic, and reconstructed data sources
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.GetRealTickData(System.Int32)">
            <summary>
            Get real tick data for buy/sell volume calculation
            Replaces synthetic volume calculations with real ATAS tick data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.RequestRealTimeTickData(System.Int32)">
            <summary>
            Request real-time tick data from event-driven cache
            Uses data collected from ATAS OnNewTrade events for authentic market data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.AggregateRealTimeTicks(System.Collections.Generic.List{ATAS.Indicators.MarketDataArg},System.Int32)">
            <summary>
            Aggregate multiple real-time ticks into a single tick data object
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ProcessRealTimeTick(ATAS.Indicators.MarketDataArg,System.Int32)">
            <summary>
            Process real-time tick data from ATAS OnNewTrade event
            This is the CORRECT way to capture real tick data in ATAS
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ProcessRealTimeDepth(ATAS.Indicators.MarketDataArg,System.Int32)">
            <summary>
            Process real-time bid/ask changes from ATAS OnBestBidAskChanged event
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ProcessRealTimeMarketDepths(System.Collections.Generic.IEnumerable{ATAS.Indicators.MarketDataArg},System.Int32)">
            <summary>
            Process real-time market depth changes from ATAS MarketDepthsChanged event
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.UpdateTickDataFromRealTrade(ATAS.Indicators.MarketDataArg,System.Int32)">
            <summary>
            Update tick data cache from real ATAS trade data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.UpdateOrderBookFromRealDepth(ATAS.Indicators.MarketDataArg,System.Int32)">
            <summary>
            Update order book cache from real ATAS depth data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.GetRealOrderBookData(System.Int32)">
            <summary>
            Get real Level 2 order book data
            Replaces simulated order book with real ATAS market depth data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.RequestRealTimeOrderBookData(System.Int32)">
            <summary>
            Request real-time order book data from event-driven cache
            Uses data collected from ATAS OnBestBidAskChanged and MarketDepthsChanged events
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ValidateDataFreshness">
            <summary>
            Validate data freshness for live trading compliance
            Ensures all data is real-time and not historical/delayed
            ENHANCED: Stricter validation for crypto futures (5-second maximum)
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ValidateTickData(OrderFlowMasterV1.Models.RealTickData)">
            <summary>
            Validate tick data authenticity and quality
            ENHANCED: Stricter validation for crypto futures live trading
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.ValidateOrderBookData(OrderFlowMasterV1.Models.RealOrderBookData)">
            <summary>
            Validate order book data authenticity and quality
            ENHANCED: Stricter validation for crypto futures live trading
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.MonitorLatency(System.Object)">
            <summary>
            Monitor latency for Australian trading environment
            Implements latency compensation and connection quality monitoring
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.MonitorDataFeedHealth">
            <summary>
            Monitor data feed health and implement redundancy
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.TriggerEmergencyShutdown(System.String,System.Int32)">
            <summary>
            Trigger emergency shutdown when fake/synthetic data is detected
            CRITICAL: This method immediately stops the strategy to prevent live trading with non-real data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataAccessLayer.TriggerDataValidationFailure(System.String,System.Int32)">
            <summary>
            Trigger data validation failure when synthetic data is detected
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Services.RealDataAccessLayer.IsEmergencyShutdownTriggered">
            <summary>
            Check if emergency shutdown has been triggered
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Services.RealDataValidationService">
            <summary>
            Real Data Validation Service for 100% live market data compliance
            Implements comprehensive validation mechanisms for Australian trading environment
            Ensures all trading decisions are made exclusively on real-time market data
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateAllDataSources(System.Int32)">
            <summary>
            Validate all data sources for live trading compliance
            Returns comprehensive validation result with detailed analysis
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.GetAustralianEnvironmentMetrics">
            <summary>
            Get current Australian environment metrics
            Specialized monitoring for geographical latency and connection quality
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateTickData(OrderFlowMasterV1.Models.RealDataValidationResult,System.Int32)">
            <summary>
            Validate tick data authenticity and freshness
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateOrderBookData(OrderFlowMasterV1.Models.RealDataValidationResult,System.Int32)">
            <summary>
            Validate order book data authenticity and freshness
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateMultiTimeframeData(OrderFlowMasterV1.Models.RealDataValidationResult,System.Int32)">
            <summary>
            Validate multi-timeframe data integrity
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateAustralianEnvironment(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Validate Australian trading environment conditions
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateDataFeedHealth(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Validate data feed health and redundancy
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.ValidateConnectionStability(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Validate connection stability
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.CalculateOverallQualityScore(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Calculate overall data quality score
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.CheckEmergencyShutdownConditions(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Check for emergency shutdown conditions
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.CalculateConnectionQualityScore">
            <summary>
            Calculate connection quality score for Australian environment
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.UpdateOptimizationSuggestions">
            <summary>
            Update optimization suggestions for Australian environment
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.PerformValidation(System.Object)">
            <summary>
            Periodic validation monitoring
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Services.RealDataValidationService.LogValidationResult(OrderFlowMasterV1.Models.RealDataValidationResult)">
            <summary>
            Log validation result details
            </summary>
        </member>
        <member name="T:OrderFlowMasterV1.Utils.CircularBuffer`1">
            <summary>
            🚀 High-performance circular buffer for rolling calculations
            Optimized for sub-5ms execution with minimal allocations
            Perfect for crypto market data streaming and momentum analysis
            </summary>
            <typeparam name="T">Type of data to store</typeparam>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.#ctor(System.Int32)">
            <summary>
            Initializes a new circular buffer with the specified capacity
            </summary>
            <param name="capacity">Maximum number of items to store</param>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.Count">
            <summary>
            Gets the number of items currently in the buffer
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.Capacity">
            <summary>
            Gets the maximum capacity of the buffer
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.IsFull">
            <summary>
            Gets whether the buffer is at full capacity
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer, overwriting the oldest item if at capacity
            </summary>
            <param name="item">Item to add</param>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.Item(System.Int32)">
            <summary>
            Gets an item at the specified index (0 = oldest, Count-1 = newest)
            </summary>
            <param name="index">Index of the item to retrieve</param>
            <returns>Item at the specified index</returns>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.Latest">
            <summary>
            Gets the most recent item added to the buffer
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.Oldest">
            <summary>
            Gets the oldest item in the buffer
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.Clear">
            <summary>
            Clears all items from the buffer
            </summary>
        </member>
        <member name="P:OrderFlowMasterV1.Utils.CircularBuffer`1.IsEmpty">
            <summary>
            Checks if the buffer contains any items
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.GetAverage">
            <summary>
            Gets the average of all numeric values in the buffer (for numeric types)
            </summary>
            <returns>Average value</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.GetSum">
            <summary>
            Gets the sum of all numeric values in the buffer (for numeric types)
            </summary>
            <returns>Sum of all values</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.ToArray">
            <summary>
            Copies all items to an array
            </summary>
            <returns>Array containing all items in chronological order (oldest first)</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.CircularBuffer`1.ToString">
            <summary>
            Gets a string representation of the buffer contents
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:OrderFlowMasterV1.Utils.PerformanceValidator">
            <summary>
            📊 Performance validation utility for Phase 1 Momentum Enhancement
            Validates less than 5ms OnCalculate performance target with Australian latency tolerance (5-10ms)
            Monitors memory usage and provides comprehensive performance analytics
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.StartMeasurement">
            <summary>
            Start performance measurement
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.StopMeasurement(System.String)">
            <summary>
            Stop measurement and record results
            </summary>
            <param name="componentName">Name of component being measured</param>
            <returns>Execution time in milliseconds</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.ValidateCVDPerformance(System.Double)">
            <summary>
            Validate CVD enhancement performance target
            </summary>
            <param name="executionTime">CVD calculation execution time</param>
            <returns>True if within target</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.ValidateFootprintPerformance(System.Double)">
            <summary>
            Validate Footprint enhancement performance target
            </summary>
            <param name="executionTime">Footprint calculation execution time</param>
            <returns>True if within target</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.GetStatistics">
            <summary>
            Get comprehensive performance statistics
            </summary>
            <returns>Performance statistics object</returns>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.GeneratePerformanceReport">
            <summary>
            Generate comprehensive performance report
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.Reset">
            <summary>
            Reset all statistics and start fresh measurement period
            </summary>
        </member>
        <member name="M:OrderFlowMasterV1.Utils.PerformanceValidator.IsPerformanceAcceptable">
            <summary>
            Check if current performance is within acceptable limits
            </summary>
            <returns>True if performance is acceptable</returns>
        </member>
        <member name="T:OrderFlowMasterV1.Utils.PerformanceStatistics">
            <summary>
            Performance statistics data structure
            </summary>
        </member>
    </members>
</doc>
