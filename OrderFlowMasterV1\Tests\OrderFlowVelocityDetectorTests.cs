using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using OrderFlowMasterV1.Components;
using OrderFlowMasterV1.Configuration;
using OrderFlowMasterV1.Models;

namespace OrderFlowMasterV1.Tests
{
    /// <summary>
    /// 🧪 Comprehensive Unit Tests for OrderFlowVelocityDetector
    /// 
    /// Validates all critical functionality according to ULTRA_PRECISION_IMPLEMENTATION_GUIDE.md:
    /// - Performance: <0.5ms execution time
    /// - Accuracy: >85% velocity spike detection
    /// - Data Compliance: 100% real live data validation
    /// - Memory: <10MB additional overhead
    /// - Integration: Successful FootprintEngine integration
    /// </summary>
    [TestClass]
    public class OrderFlowVelocityDetectorTests
    {
        private OrderFlowVelocityDetector _detector;
        private OrderFlowConfiguration _config;
        private AssetConfiguration _assetConfig;
        private MockChartStrategy _mockStrategy;
        private MockDataSeries _mockVolume;
        private MockDataSeries _mockDelta;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test configuration
            _config = new OrderFlowConfiguration
            {
                EnablePerformanceMonitoring = true,
                EnableDebugLogging = false
            };

            _assetConfig = new AssetConfiguration
            {
                AssetType = AssetType.ETH,
                CurrentVolumeThreshold = 1000m,
                CurrentDeltaThreshold = 500m
            };

            _mockStrategy = new MockChartStrategy();
            _mockVolume = new MockDataSeries();
            _mockDelta = new MockDataSeries();

            _detector = new OrderFlowVelocityDetector(_config, _assetConfig, _mockStrategy);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _detector?.Dispose();
        }

        #region Performance Validation Tests

        /// <summary>
        /// Test 1.1: Validate execution time <0.5ms per calculation
        /// CRITICAL: Must meet performance target for 15-second scalping
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidatePerformanceTarget()
        {
            // Arrange
            GenerateRealisticMarketData(500); // 500 bars of realistic data
            var testBar = 350; // Sufficient history for baseline calculation

            // Act
            var startTime = DateTime.UtcNow;
            var result = _detector.CalculateVelocitySpike(testBar, _mockVolume, _mockDelta);
            var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Assert
            Assert.IsTrue(executionTime < 0.5, 
                $"Execution time {executionTime:F3}ms exceeds 0.5ms target");
            Assert.IsNotNull(result, "Result should not be null");
            Assert.AreEqual(testBar, result.Bar, "Bar index should match");
        }

        /// <summary>
        /// Test 1.2: Validate memory usage <10MB additional overhead
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidateMemoryUsage()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);
            GenerateRealisticMarketData(1000);

            // Act - Process multiple calculations
            for (int i = 300; i < 1000; i++)
            {
                _detector.CalculateVelocitySpike(i, _mockVolume, _mockDelta);
            }

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = (finalMemory - initialMemory) / 1024.0 / 1024.0; // Convert to MB

            // Assert
            Assert.IsTrue(memoryIncrease < 10.0, 
                $"Memory increase {memoryIncrease:F2}MB exceeds 10MB target");
        }

        #endregion

        #region Velocity Spike Detection Tests

        /// <summary>
        /// Test 1.3: Validate velocity spike detection accuracy >85%
        /// Uses known spike patterns from realistic market scenarios
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidateSpikeDetectionAccuracy()
        {
            // Arrange
            var spikeScenarios = GenerateKnownSpikeScenarios();
            int correctDetections = 0;
            int totalScenarios = spikeScenarios.Count;

            // Act
            foreach (var scenario in spikeScenarios)
            {
                SetupScenarioData(scenario);
                var result = _detector.CalculateVelocitySpike(scenario.TestBar, _mockVolume, _mockDelta);
                
                if (result.HasVelocitySpike == scenario.ExpectedSpike)
                {
                    correctDetections++;
                }
            }

            // Assert
            var accuracy = (decimal)correctDetections / totalScenarios;
            Assert.IsTrue(accuracy > 0.85m, 
                $"Spike detection accuracy {accuracy:P} must be >85% (got {correctDetections}/{totalScenarios})");
        }

        /// <summary>
        /// Test 1.4: Validate 5x baseline threshold detection
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidateThresholdDetection()
        {
            // Arrange - Create scenario with exactly 5x baseline spike
            GenerateBaselineData(300, 100m); // Baseline velocity ~100
            GenerateVelocitySpike(350, 500m); // 5x spike at bar 350

            // Act
            var result = _detector.CalculateVelocitySpike(350, _mockVolume, _mockDelta);

            // Assert
            Assert.IsTrue(result.VelocitySpike >= 5.0m, 
                $"Velocity spike {result.VelocitySpike:F2}x should be >=5x threshold");
            Assert.IsTrue(result.HasVelocitySpike, "Should detect velocity spike at 5x threshold");
            Assert.IsTrue(result.SpikeStrength > 0.5m, "Spike strength should be significant");
        }

        #endregion

        #region Real Data Compliance Tests

        /// <summary>
        /// Test 1.5: Validate 100% real live data compliance
        /// CRITICAL: Must reject synthetic/mock data and trigger emergency shutdown
        /// </summary>
        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void OrderFlowVelocityDetector_RejectSyntheticData()
        {
            // Arrange - Generate obvious synthetic data pattern
            GenerateSyntheticDataPattern();

            // Act - Should trigger emergency shutdown
            _detector.CalculateVelocitySpike(100, _mockVolume, _mockDelta);

            // Assert - Exception should be thrown (handled by ExpectedException)
        }

        /// <summary>
        /// Test 1.6: Validate data age compliance (<5 seconds)
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidateDataAgeCompliance()
        {
            // Arrange
            GenerateRealisticMarketData(100);

            // Act - Process with current timestamp (should pass)
            var result = _detector.CalculateVelocitySpike(50, _mockVolume, _mockDelta);

            // Assert
            Assert.IsNotNull(result, "Should process current data successfully");
            Assert.AreNotEqual(-1, result.Bar, "Should return valid result for current data");
        }

        #endregion

        #region Integration Tests

        /// <summary>
        /// Test 1.7: Validate integration with FootprintEngine patterns
        /// </summary>
        [TestMethod]
        public void OrderFlowVelocityDetector_ValidateFootprintIntegration()
        {
            // Arrange
            GenerateRealisticMarketData(400);
            var testBar = 350;

            // Act
            var result = _detector.CalculateVelocitySpike(testBar, _mockVolume, _mockDelta);
            var velocitySpike = _detector.GetVelocitySpike(testBar);
            var hasSpike = _detector.HasVelocitySpike(testBar);

            // Assert
            Assert.AreEqual(result.VelocitySpike, velocitySpike, "GetVelocitySpike should match result");
            Assert.AreEqual(result.HasVelocitySpike, hasSpike, "HasVelocitySpike should match result");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generate realistic market data for testing
        /// </summary>
        private void GenerateRealisticMarketData(int barCount)
        {
            var random = new Random(42); // Fixed seed for reproducible tests
            
            for (int i = 0; i < barCount; i++)
            {
                // Generate realistic volume (50-5000 range with occasional spikes)
                var baseVolume = 100m + (decimal)(random.NextDouble() * 500);
                var volumeSpike = random.NextDouble() < 0.05 ? (decimal)(random.NextDouble() * 2000) : 0;
                _mockVolume.SetValue(i, baseVolume + volumeSpike);

                // Generate realistic delta (-500 to +500 range)
                var delta = (decimal)((random.NextDouble() - 0.5) * 1000);
                _mockDelta.SetValue(i, delta);
            }
        }

        /// <summary>
        /// Generate baseline data with consistent velocity
        /// </summary>
        private void GenerateBaselineData(int barCount, decimal baselineVelocity)
        {
            for (int i = 0; i < barCount; i++)
            {
                _mockVolume.SetValue(i, baselineVelocity * 0.7m); // Volume component
                _mockDelta.SetValue(i, baselineVelocity * 0.3m);  // Delta component
            }
        }

        /// <summary>
        /// Generate velocity spike at specific bar
        /// </summary>
        private void GenerateVelocitySpike(int spikeBar, decimal spikeVelocity)
        {
            // Create spike over 15-second window
            for (int i = 0; i < 15; i++)
            {
                var bar = spikeBar - i;
                if (bar >= 0)
                {
                    _mockVolume.SetValue(bar, spikeVelocity * 0.7m);
                    _mockDelta.SetValue(bar, spikeVelocity * 0.3m);
                }
            }
        }

        /// <summary>
        /// Generate known spike scenarios for accuracy testing
        /// </summary>
        private List<SpikeScenario> GenerateKnownSpikeScenarios()
        {
            return new List<SpikeScenario>
            {
                new SpikeScenario { TestBar = 350, ExpectedSpike = true, Description = "5x baseline spike" },
                new SpikeScenario { TestBar = 400, ExpectedSpike = true, Description = "10x baseline spike" },
                new SpikeScenario { TestBar = 450, ExpectedSpike = false, Description = "2x baseline (below threshold)" },
                new SpikeScenario { TestBar = 500, ExpectedSpike = false, Description = "Normal market activity" }
            };
        }

        /// <summary>
        /// Setup data for specific spike scenario
        /// </summary>
        private void SetupScenarioData(SpikeScenario scenario)
        {
            GenerateRealisticMarketData(600);
            
            if (scenario.ExpectedSpike)
            {
                var spikeMultiplier = scenario.Description.Contains("10x") ? 10m : 5m;
                GenerateVelocitySpike(scenario.TestBar, 100m * spikeMultiplier);
            }
        }

        /// <summary>
        /// Generate obvious synthetic data pattern for compliance testing
        /// </summary>
        private void GenerateSyntheticDataPattern()
        {
            // Create identical values (common in mock data)
            for (int i = 0; i < 100; i++)
            {
                _mockVolume.SetValue(i, 1000m); // Identical volume
                _mockDelta.SetValue(i, 500m);   // Identical delta
            }
        }

        #endregion

        #region Test Data Structures

        private class SpikeScenario
        {
            public int TestBar { get; set; }
            public bool ExpectedSpike { get; set; }
            public string Description { get; set; }
        }

        #endregion
    }

    #region Mock Classes for Testing

    /// <summary>
    /// Mock ChartStrategy for testing
    /// </summary>
    public class MockChartStrategy : ChartStrategy
    {
        private readonly List<string> _logMessages = new List<string>();

        public MockChartStrategy() : base(true) { }

        public override void LogInfo(string message)
        {
            _logMessages.Add($"INFO: {message}");
        }

        public override void LogWarn(string message)
        {
            _logMessages.Add($"WARN: {message}");
        }

        public override void LogError(string message)
        {
            _logMessages.Add($"ERROR: {message}");
        }

        public List<string> GetLogMessages() => new List<string>(_logMessages);
        public void ClearLogs() => _logMessages.Clear();
    }

    /// <summary>
    /// Mock DataSeries for testing
    /// </summary>
    public class MockDataSeries : IDataSeries<decimal>
    {
        private readonly Dictionary<int, decimal> _data = new Dictionary<int, decimal>();

        public decimal this[int index]
        {
            get => _data.TryGetValue(index, out var value) ? value : 0m;
            set => _data[index] = value;
        }

        public void SetValue(int index, decimal value)
        {
            _data[index] = value;
        }

        public int Count => _data.Count;

        // IDataSeries implementation
        public decimal GetValueByIndex(int index) => this[index];
    }

    #endregion
}
